(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[19],{2922:(t,e,s)=>{"use strict";s.d(e,{E:()=>R});var i="undefined"==typeof window||"Deno"in globalThis;function n(){}function r(t,e){return"function"==typeof t?t(e):t}function a(t,e){let{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a){if(i){if(e.queryHash!==u(a,e.options))return!1}else if(!h(e.queryKey,a))return!1}if("all"!==s){let t=e.isActive();if("active"===s&&!t||"inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&(!n||n===e.state.fetchStatus)&&(!r||!!r(e))}function o(t,e){let{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(c(e.options.mutationKey)!==c(r))return!1}else if(!h(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&(!n||!!n(e))}function u(t,e){return(e?.queryKeyHashFn||c)(t)}function c(t){return JSON.stringify(t,(t,e)=>d(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function h(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(s=>h(t[s],e[s]))}function l(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function d(t){if(!f(t))return!1;let e=t.constructor;if(void 0===e)return!0;let s=e.prototype;return!!f(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function f(t){return"[object Object]"===Object.prototype.toString.call(t)}function p(t,e,s=0){let i=[...t,e];return s&&i.length>s?i.slice(1):i}function y(t,e,s=0){let i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var m=Symbol();function v(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==m?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}var g=t=>setTimeout(t,0),b=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=g,r=i=>{e?t.push(i):n(()=>{s(i)})},a=()=>{let e=t;t=[],e.length&&n(()=>{i(()=>{e.forEach(t=>{s(t)})})})};return{batch:t=>{let s;e++;try{s=t()}finally{--e||a()}return s},batchCalls:t=>(...e)=>{r(()=>{t(...e)})},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},C=new class extends w{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}},O=new class extends w{#i=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){let e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#i!==t&&(this.#i=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#i}};function S(t){return Math.min(1e3*2**t,3e4)}function q(t){return(t??"online")!=="online"||O.isOnline()}var P=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function E(t){return t instanceof P}function A(t){let e,s=!1,n=0,r=!1,a=function(){let t,e,s=new Promise((s,i)=>{t=s,e=i});function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}(),o=()=>C.isFocused()&&("always"===t.networkMode||O.isOnline())&&t.canRun(),u=()=>q(t.networkMode)&&t.canRun(),c=s=>{r||(r=!0,t.onSuccess?.(s),e?.(),a.resolve(s))},h=s=>{r||(r=!0,t.onError?.(s),e?.(),a.reject(s))},l=()=>new Promise(s=>{e=t=>{(r||o())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,r||t.onContinue?.()}),d=()=>{let e;if(r)return;let a=0===n?t.initialPromise:void 0;try{e=a??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(c).catch(e=>{if(r)return;let a=t.retry??3*!i,u=t.retryDelay??S,c="function"==typeof u?u(n,e):u,f=!0===a||"number"==typeof a&&n<a||"function"==typeof a&&a(n,e);if(s||!f)return void h(e);n++,t.onFail?.(n,e),new Promise(t=>{setTimeout(t,c)}).then(()=>o()?void 0:l()).then(()=>{s?h(e):d()})})};return{promise:a,cancel:e=>{r||(h(new P(e)),t.abort?.())},continue:()=>(e?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():l().then(d),a)}}var F=class{#n;destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&(this.#n=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i?1/0:3e5))}clearGcTimeout(){this.#n&&(clearTimeout(this.#n),this.#n=void 0)}},x=class extends F{#r;#a;#o;#u;#c;#h;#l;constructor(t){super(),this.#l=!1,this.#h=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#u=t.client,this.#o=this.#u.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#r=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#r,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(t){this.options={...this.#h,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){var s,i;let n=(s=this.state.data,"function"==typeof(i=this.options).structuralSharing?i.structuralSharing(s,t):!1!==i.structuralSharing?function t(e,s){if(e===s)return e;let i=l(e)&&l(s);if(i||d(e)&&d(s)){let n=i?e:Object.keys(e),r=n.length,a=i?s:Object.keys(s),o=a.length,u=i?[]:{},c=0;for(let r=0;r<o;r++){let o=i?r:a[r];(!i&&n.includes(o)||i)&&void 0===e[o]&&void 0===s[o]?(u[o]=void 0,c++):(u[o]=t(e[o],s[o]),u[o]===e[o]&&void 0!==e[o]&&c++)}return r===o&&c===r?e:u}return s}(s,t):t);return this.#d({data:n,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),n}setState(t,e){this.#d({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#c?.promise;return this.#c?.cancel(t),e?e.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#r)}isActive(){return this.observers.some(t=>{var e;return!1!==(e=t.options.enabled,"function"==typeof e?e(this):e)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(t||0)-Date.now(),0)}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#c&&(this.#l?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#l=!0,s.signal)})},n={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:()=>{let t=v(this.options,e),s={client:this.#u,queryKey:this.queryKey,meta:this.meta};return(i(s),this.#l=!1,this.options.persister)?this.options.persister(t,s,this):t(s)}};i(n),this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let r=t=>{E(t)&&t.silent||this.#d({type:"error",error:t}),E(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#c=A({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0===t)return void r(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){r(t);return}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:r,onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":var s;return{...e,...(s=e.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:q(this.options.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=t.error;if(E(i)&&i.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...e,error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),b.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:t})})}},_=class extends w{constructor(t={}){super(),this.config=t,this.#f=new Map}#f;build(t,e,s){let i=e.queryKey,n=e.queryHash??u(i,e),r=this.get(n);return r||(r=new x({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){this.#f.has(t.queryHash)||(this.#f.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#f.get(t.queryHash);e&&(t.destroy(),e===t&&this.#f.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){b.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#f.get(t)}getAll(){return[...this.#f.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>a(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>a(t,e)):e}notify(t){b.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){b.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){b.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},j=class extends F{#p;#y;#c;constructor(t){super(),this.mutationId=t.mutationId,this.#y=t.mutationCache,this.#p=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#p.includes(t)||(this.#p.push(t),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#p=this.#p.filter(e=>e!==t),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#d({type:"continue"})};this.#c=A({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let s="pending"===this.state.status,i=!this.#c.canStart();try{if(s)e();else{this.#d({type:"pending",variables:t,isPaused:i}),await this.#y.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#d({type:"pending",context:e,variables:t,isPaused:i})}let n=await this.#c.start();return await this.#y.config.onSuccess?.(n,t,this.state.context,this),await this.options.onSuccess?.(n,t,this.state.context),await this.#y.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,t,this.state.context),this.#d({type:"success",data:n}),n}catch(e){try{throw await this.#y.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#y.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#d({type:"error",error:e})}}finally{this.#y.runNext(this)}}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),b.batch(()=>{this.#p.forEach(e=>{e.onMutationUpdate(t)}),this.#y.notify({mutation:this,type:"updated",action:t})})}},D=class extends w{constructor(t={}){super(),this.config=t,this.#m=new Set,this.#v=new Map,this.#g=0}#m;#v;#g;build(t,e,s){let i=new j({mutationCache:this,mutationId:++this.#g,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#m.add(t);let e=M(t);if("string"==typeof e){let s=this.#v.get(e);s?s.push(t):this.#v.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#m.delete(t)){let e=M(t);if("string"==typeof e){let s=this.#v.get(e);if(s)if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#v.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=M(t);if("string"!=typeof e)return!0;{let s=this.#v.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=M(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#v.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){b.batch(()=>{this.#m.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>o(e,t))}findAll(t={}){return this.getAll().filter(e=>o(t,e))}notify(t){b.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return b.batch(()=>Promise.all(t.map(t=>t.continue().catch(n))))}};function M(t){return t.options.scope?.id}function T(t){return{onFetch:(e,s)=>{let i=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,r=e.state.data?.pages||[],a=e.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,c=async()=>{let s=!1,c=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)})},h=v(e.options,e.fetchOptions),l=async(t,i,n)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);let r={client:e.client,queryKey:e.queryKey,pageParam:i,direction:n?"backward":"forward",meta:e.options.meta};c(r);let a=await h(r),{maxPages:o}=e.options,u=n?y:p;return{pages:u(t.pages,a,o),pageParams:u(t.pageParams,i,o)}};if(n&&r.length){let t="backward"===n,e={pages:r,pageParams:a},s=(t?function(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}:k)(i,e);o=await l(e,s,t)}else{let e=t??r.length;do{let t=0===u?a[0]??i.initialPageParam:k(i,o);if(u>0&&null==t)break;o=await l(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function k(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}var R=class{#b;#y;#h;#w;#C;#O;#S;#q;constructor(t={}){this.#b=t.queryCache||new _,this.#y=t.mutationCache||new D,this.#h=t.defaultOptions||{},this.#w=new Map,this.#C=new Map,this.#O=0}mount(){this.#O++,1===this.#O&&(this.#S=C.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#q=O.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#O--,0===this.#O&&(this.#S?.(),this.#S=void 0,this.#q?.(),this.#q=void 0)}isFetching(t){return this.#b.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#y.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#b.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#b.build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(r(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return this.#b.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){let i=this.defaultQueryOptions({queryKey:t}),n=this.#b.get(i.queryHash),r=n?.state.data,a="function"==typeof e?e(r):e;if(void 0!==a)return this.#b.build(this,i).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return b.batch(()=>this.#b.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#b.get(e.queryHash)?.state}removeQueries(t){let e=this.#b;b.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#b;return b.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e};return Promise.all(b.batch(()=>this.#b.findAll(t).map(t=>t.cancel(s)))).then(n).catch(n)}invalidateQueries(t,e={}){return b.batch(()=>(this.#b.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(b.batch(()=>this.#b.findAll(t).filter(t=>!t.isDisabled()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(n)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(n)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#b.build(this,e);return s.isStaleByTime(r(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(n).catch(n)}fetchInfiniteQuery(t){return t.behavior=T(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(n).catch(n)}ensureInfiniteQueryData(t){return t.behavior=T(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return O.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#y}getDefaultOptions(){return this.#h}setDefaultOptions(t){this.#h=t}setQueryDefaults(t,e){this.#w.set(c(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#w.values()],s={};return e.forEach(e=>{h(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#C.set(c(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#C.values()],s={};return e.forEach(e=>{h(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#h.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=u(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===m&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#h.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#b.clear(),this.#y.clear()}}},3864:(t,e,s)=>{"use strict";s.d(e,{Analytics:()=>p});var i=s(2115),n=s(5695),r=s(1890),a=()=>{window.va||(window.va=function(){for(var t=arguments.length,e=Array(t),s=0;s<t;s++)e[s]=arguments[s];(window.vaq=window.vaq||[]).push(e)})};function o(){return"undefined"!=typeof window}function u(){return"production"}function c(){return"development"===((o()?window.vam:u())||"production")}function h(t){return new RegExp("/".concat(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function l(t){return(0,i.useEffect)(()=>{var e;t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend))},[t.beforeSend]),(0,i.useEffect)(()=>{var e;!function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!o())return;!function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===t){window.vam=u();return}window.vam=t}(e.mode),a(),e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend));let s=e.scriptSrc?e.scriptSrc:c()?"https://va.vercel-scripts.com/v1/script.debug.js":e.basePath?"".concat(e.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(s,'"]')))return;let i=document.createElement("script");i.src=s,i.defer=!0,i.dataset.sdkn="@vercel/analytics"+(e.framework?"/".concat(e.framework):""),i.dataset.sdkv="1.5.0",e.disableAutoTrack&&(i.dataset.disableAutoTrack="1"),e.endpoint?i.dataset.endpoint=e.endpoint:e.basePath&&(i.dataset.endpoint="".concat(e.basePath,"/insights")),e.dsn&&(i.dataset.dsn=e.dsn),i.onerror=()=>{let t=c()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log("[Vercel Web Analytics] Failed to load script from ".concat(s,". ").concat(t))},c()&&!1===e.debug&&(i.dataset.debug="false"),document.head.appendChild(i)}({framework:t.framework||"react",basePath:null!=(e=t.basePath)?e:function(){if(void 0!==r&&void 0!==r.env)return r.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==t.route&&{disableAutoTrack:!0},...t})},[]),(0,i.useEffect)(()=>{t.route&&t.path&&function(t){var e;let{route:s,path:i}=t;null==(e=window.va)||e.call(window,"pageview",{route:s,path:i})}({route:t.route,path:t.path})},[t.route,t.path]),null}var d=()=>{let t=(0,n.useParams)(),e=(0,n.useSearchParams)(),s=(0,n.usePathname)();return t?{route:function(t,e){if(!t||!e)return t;let s=t;try{let t=Object.entries(e);for(let[e,i]of t)if(!Array.isArray(i)){let t=h(i);t.test(s)&&(s=s.replace(t,"/[".concat(e,"]")))}for(let[e,i]of t)if(Array.isArray(i)){let t=h(i.join("/"));t.test(s)&&(s=s.replace(t,"/[...".concat(e,"]")))}return s}catch(e){return t}}(s,Object.keys(t).length?t:Object.fromEntries(e.entries())),path:s}:{route:null,path:s}};function f(t){let{route:e,path:s}=d();return i.createElement(l,{path:s,route:e,...t,basePath:function(){if(void 0!==r&&void 0!==r.env)return r.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}(),framework:"next"})}function p(t){return i.createElement(i.Suspense,{fallback:null},i.createElement(f,{...t}))}},5356:t=>{t.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},6063:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sendGTMEvent=void 0,e.GoogleTagManager=function(t){let{gtmId:e,gtmScriptUrl:s="https://www.googletagmanager.com/gtm.js",dataLayerName:o="dataLayer",auth:u,preview:c,dataLayer:h,nonce:l}=t;a=o;let d="dataLayer"!==o?"&l=".concat(o):"";return(0,n.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(h?"w[l].push(".concat(JSON.stringify(h),")"):"","\n      })(window,'").concat(o,"');")},nonce:l}),(0,i.jsx)(r.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(s,"?id=").concat(e).concat(d).concat(u?"&gtm_auth=".concat(u):"").concat(c?"&gtm_preview=".concat(c,"&gtm_cookies_win=x"):""),nonce:l})]})};let i=s(5155),n=s(2115),r=function(t){return t&&t.__esModule?t:{default:t}}(s(3554)),a="dataLayer";e.sendGTMEvent=(t,e)=>{let s=e||a;window[s]=window[s]||[],window[s].push(t)}},6259:(t,e,s)=>{"use strict";let i;Object.defineProperty(e,"__esModule",{value:!0}),e.GoogleAnalytics=function(t){let{gaId:e,debugMode:s,dataLayerName:o="dataLayer",nonce:u}=t;return void 0===i&&(i=o),(0,r.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(o,"'] = window['").concat(o,"'] || [];\n          function gtag(){window['").concat(o,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(e,"' ").concat(s?",{ 'debug_mode': true }":"",");")},nonce:u}),(0,n.jsx)(a.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(e),nonce:u})]})},e.sendGAEvent=function(){for(var t=arguments.length,e=Array(t),s=0;s<t;s++)e[s]=arguments[s];if(void 0===i)return void console.warn("@next/third-parties: GA has not been initialized");window[i]?window[i].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(i," does not exist"))};let n=s(5155),r=s(2115),a=function(t){return t&&t.__esModule?t:{default:t}}(s(3554))},8930:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){let{html:e,height:s=null,width:r=null,children:a,dataNtpc:o=""}=t;return(0,n.useEffect)(()=>{o&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(o)}})},[o]),(0,i.jsxs)(i.Fragment,{children:[a,e?(0,i.jsx)("div",{style:{height:null!=s?"".concat(s,"px"):"auto",width:null!=r?"".concat(r,"px"):"auto"},"data-ntpc":o,dangerouslySetInnerHTML:{__html:e}}):null]})};let i=s(5155),n=s(2115)}}]);