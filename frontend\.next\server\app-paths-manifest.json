{"/api/analytics/post-view/route": "app/api/analytics/post-view/route.js", "/api/clinics/[slug]/route": "app/api/clinics/[slug]/route.js", "/api/clinics/route": "app/api/clinics/route.js", "/api/protected-data/route": "app/api/protected-data/route.js", "/api/protected-example/route": "app/api/protected-example/route.js", "/api/revalidate/blog/route": "app/api/revalidate/blog/route.js", "/api/revalidate/categories/route": "app/api/revalidate/categories/route.js", "/api/revalidate/clinics/route": "app/api/revalidate/clinics/route.js", "/api/revalidate/conditions/route": "app/api/revalidate/conditions/route.js", "/api/revalidate/practitioners/route": "app/api/revalidate/practitioners/route.js", "/api/revalidate/route": "app/api/revalidate/route.js", "/api/revalidate/specialities/route": "app/api/revalidate/specialities/route.js", "/api/secure-example/route": "app/api/secure-example/route.js", "/api/strapi-proxy/route": "app/api/strapi-proxy/route.js", "/api/test/route": "app/api/test/route.js", "/sitemap-blog.xml/route": "app/sitemap-blog.xml/route.js", "/sitemap-clinics.xml/route": "app/sitemap-clinics.xml/route.js", "/sitemap-index.xml/route": "app/sitemap-index.xml/route.js", "/sitemap-practitioners.xml/route": "app/sitemap-practitioners.xml/route.js", "/sitemaps.xml/route": "app/sitemaps.xml/route.js", "/test.xml/route": "app/test.xml/route.js", "/blog/sitemap.xml/route": "app/blog/sitemap.xml/route.js", "/clinics/sitemap.xml/route": "app/clinics/sitemap.xml/route.js", "/practitioners/sitemap.xml/route": "app/practitioners/sitemap.xml/route.js", "/robots.txt/route": "app/robots.txt/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/about-us/page": "app/about-us/page.js", "/account/page": "app/account/page.js", "/affiliate-disclosure/page": "app/affiliate-disclosure/page.js", "/api/example-server-component/page": "app/api/example-server-component/page.js", "/blog/tags/page": "app/blog/tags/page.js", "/blog/tags/[slug]/page": "app/blog/tags/[slug]/page.js", "/blog/categories/page": "app/blog/categories/page.js", "/optimized-client-example/page": "app/optimized-client-example/page.js", "/page": "app/page.js", "/optimized-example/page": "app/optimized-example/page.js", "/privacy/page": "app/privacy/page.js", "/terms/page": "app/terms/page.js", "/blog/[slug]/page": "app/blog/[slug]/page.js", "/blog/authors/[slug]/page": "app/blog/authors/[slug]/page.js", "/blog/categories/[slug]/page": "app/blog/categories/[slug]/page.js", "/blog/authors/page": "app/blog/authors/page.js", "/categories/page": "app/categories/page.js", "/categories/[slug]/[cityStateSlug]/page": "app/categories/[slug]/[cityStateSlug]/page.js", "/blog/page": "app/blog/page.js", "/categories/[slug]/page": "app/categories/[slug]/page.js", "/conditions/page": "app/conditions/page.js", "/clinics/[slug]/page": "app/clinics/[slug]/page.js", "/cors-test/page": "app/cors-test/page.js", "/contact/page": "app/contact/page.js", "/clinics/page": "app/clinics/page.js", "/forgot-password/page": "app/forgot-password/page.js", "/signin/page": "app/signin/page.js", "/signup/page": "app/signup/page.js", "/conditions/[slug]/page": "app/conditions/[slug]/page.js", "/practitioners/page": "app/practitioners/page.js", "/practitioners/[slug]/page": "app/practitioners/[slug]/page.js", "/specialities/page": "app/specialities/page.js", "/specialities/[slug]/page": "app/specialities/[slug]/page.js", "/search/page": "app/search/page.js"}