"use strict";(()=>{var e={};e.id=7150,e.ids=[7150],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10448:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v,dynamic:()=>f,dynamicParams:()=>y,generateMetadata:()=>j,generateStaticParams:()=>h,revalidate:()=>m});var i=a(37413),r=a(4536),s=a.n(r),n=a(73993),l=a(58446),o=a(39916),c=a(14007),d=a(10592),p=a(82158),u=a(74124),g=a(69290);let m=43200,y=!0;async function h(){try{let e=await l.$.specialties.getAllSlugs({cache:"force-cache",next:{revalidate:43200,tags:["strapi-specialties-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return console.log(`Pre-rendering ${e.data.length} specialty detail pages`),e.data.filter(e=>null!==e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return[]}catch(e){return console.error("Error fetching specialty slugs for generateStaticParams:",e),[]}}let f="force-dynamic",x="https://nice-badge-2130241d6c.strapiapp.com",b=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";console.log("Using STRAPI_URL:",x||"Not set"),console.log("Using site URL for canonical URLs:",b);let w=e=>{if(console.log("getStrapiMediaUrl input:",JSON.stringify({type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e},null,2)),!e)return null;if("string"==typeof e)return(console.log("URL is a string:",e),e.startsWith("http://")||e.startsWith("https://"))?e:`${x}${e}`;if(e&&"object"==typeof e){console.log("URL is an object with keys:",Object.keys(e));let t=e.url||e.data?.attributes?.url||e.data?.url||null;if(t)return(console.log("Extracted URL from object:",t),t.startsWith("http://")||t.startsWith("https://"))?t:`${x}${t}`}return console.warn("Could not extract URL from:",e),null};async function $(e){try{console.log(`Fetching specialty data for slug: ${e}`);let t=await l.$.specialties.getBySlug(e,{populate:{featuredImage:!0,seo:{populate:"*"}},next:{tags:["strapi-specialties-list",`strapi-specialty-${e}`],revalidate:43200},cache:"force-cache"});if(console.log("Specialty API response structure:",JSON.stringify({hasData:!!t,hasDataProperty:!!t?.data,isDataArray:Array.isArray(t?.data),dataLength:Array.isArray(t?.data)?t.data.length:"not an array",firstItemHasId:Array.isArray(t?.data)&&t.data.length>0?!!t.data[0].id:"no items"},null,2)),t?.data&&Array.isArray(t.data)&&t.data.length>0){let e=t.data[0];console.log("First specialty item structure:",JSON.stringify({id:e.id,hasAttributes:!!e.attributes,attributesKeys:e.attributes?Object.keys(e.attributes):[],hasSlug:!!e.attributes?.slug,slug:e.attributes?.slug||"no slug",hasDirectName:!!e.name,hasDirectSlug:!!e.slug,directName:e.name||"no direct name",directSlug:e.slug||"no direct slug"},null,2)),console.log("Complete first specialty item:",JSON.stringify(e,null,2))}if(t?.data){if(Array.isArray(t.data)&&t.data.length>0){let a=t.data[0];if(a&&a.id){let t=a.attributes||{},i=t.name||a.name||"Unnamed Specialty",r=e;t.slug?r=t.slug:a.slug&&(r=a.slug),r&&"no slug"!==r||(r=i&&"Unnamed Specialty"!==i?i.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):`specialty-${a.id}`);let s={id:a.id.toString(),documentId:a.documentId||t.documentId||"",name:i,slug:r,description:t.description||a.description||"",featuredImage:t.featuredImage||a.featuredImage||null,clinics:[],practitioners:[]};return console.log(`Successfully processed specialty: ${s.name} with slug: ${s.slug}`),s}}else if(!Array.isArray(t.data)&&t.data.id){let a=t.data,i=a.attributes||{},r=i.name||a.name||"Unnamed Specialty",s=e;i.slug?s=i.slug:a.slug&&(s=a.slug),s&&"no slug"!==s||(s=r&&"Unnamed Specialty"!==r?r.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):`specialty-${a.id}`);let n={id:a.id.toString(),documentId:a.documentId||i.documentId||"",name:r,slug:s,description:i.description||a.description||"",featuredImage:i.featuredImage||a.featuredImage||null,clinics:[],practitioners:[]};return console.log(`Successfully processed specialty: ${n.name} with slug: ${n.slug}`),n}}return console.warn(`No valid specialty data found for slug: ${e}, creating fallback`),{id:`fallback-${Date.now()}`,documentId:"",name:e.replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()),slug:e,featuredImage:null,clinics:[],practitioners:[]}}catch(t){return console.error(`Error fetching specialty with slug ${e}:`,t),{id:`error-${Date.now()}`,documentId:"",name:e.replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()),slug:e,featuredImage:null,clinics:[],practitioners:[]}}}async function j({params:e}){let t,a=await e,i=await $(a.slug);if(!i)return{title:"Specialty Not Found | Natural Healing Now",description:"The requested specialty could not be found."};let r=i?.attributes||i||{},s=r.seo,n=`${r.name||i.name||"Specialty"} | Natural Healing Now`,l=r.description||i.description||`Learn about ${r.name||i.name||"this specialty"} and find related clinics and practitioners.`,o=s?.metaTitle||n,c=s?.metaDescription||l,d=w(s?.metaImage),u=`/specialities/${i.slug}`,g=s?.canonicalURL||(b?`${b}${u}`:u);s?.openGraph?.image?t=(0,p.Rb)(w(s.openGraph.image)):s?.metaImage&&(t=(0,p.Rb)(d)),console.log("Specialty og:image:",{openGraphImage:s?.openGraph?.image,metaImage:s?.metaImage,ogImageUrl:t});let m=s?.openGraph?{title:s.openGraph.title||o,description:s.openGraph.description||c,url:s.openGraph.url||g,type:s.openGraph.type||"website",siteName:s.openGraph.siteName||"Natural Healing Now",...t&&{images:[{url:t}]}}:{title:o,description:c,url:g,type:"website",siteName:"Natural Healing Now",...t&&{images:[{url:t}]}};return{title:o,description:c,alternates:{canonical:g},openGraph:m,twitter:{card:"summary_large_image",title:s?.openGraph?.title||o,description:s?.openGraph?.description||c,images:t?[t]:[]}}}async function v({params:e,searchParams:t}){let{query:a="",location:r="",page:p,tab:m="clinics"}=await t,y=await e,h=y.slug,f=Number(p)||1,x="practitioners"===m?"practitioners":"clinics",b=await $(h);b||(0,o.notFound)();let{name:j="Unnamed Specialty",description:v=null,featuredImage:N=null}=b,S=w(N),I=0,P=0,A=1,C=a||r?{cache:"no-store"}:{next:{tags:["strapi-specialties-list",`strapi-specialty-${h}`],revalidate:43200},cache:"force-cache"};try{let e=await l.$.clinics.getAll({specialtySlug:h,pagination:{page:1,pageSize:1}});I=e?.meta?.pagination?.total||0;let t=await l.$.practitioners.getAll({specialtySlug:h,pagination:{page:1,pageSize:1}});P=t?.meta?.pagination?.total||0,console.log(`Counts for specialty ${h}: Clinics=${I}, Practitioners=${P}`)}catch(e){console.error("Error fetching counts for specialty page:",e)}let _=[],q=1;try{console.log(`Fetching clinics for specialty ${h}, activeTab: ${x}, query: ${a}, location: ${r}, currentPage: ${f}`);let e=await l.$.clinics.getAll({specialtySlug:h,query:"clinics"===x?a:"",location:"clinics"===x?r:"",page:"clinics"===x?f:1,...C});console.log(`Clinic response data length for specialty ${h}: ${e?.data?.length||0}`),_=(e?.data||[]).map(e=>{let t=e.attributes||e;e?.id&&!t.id&&(t.id=e.id);if(console.log("Raw clinic data structure:",JSON.stringify({hasData:!!t,id:t?.id,name:t?.name,keys:t?Object.keys(t):[]},null,2)),!t)return console.warn("Skipping clinic due to missing data."),null;if(!t.id||!t.name)return console.warn(`Skipping invalid clinic data: ID ${t?.id}`),null;let a=w(t.logo),i=w(t.featuredImage);return{id:String(t.id),name:t.name,slug:t.slug||`clinic-${t.id}`,description:t.description,logo:a,featuredImage:i,address:t.address||{city:"Unknown",stateProvince:"N/A"},contactInfo:t.contactInfo,isVerified:t.isVerified||!1}}).filter(Boolean),console.log(`Transformed clinics array length for specialty ${h}: ${_.length}`),q=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching clinics for specialty ${h}:`,e)}let U=[],k=1;try{console.log(`Fetching practitioners for specialty ${h}, activeTab: ${x}, query: ${a}, location: ${r}, currentPage: ${f}`);let e=await l.$.practitioners.getAll({specialtySlug:h,query:"practitioners"===x?a:"",location:"practitioners"===x?r:"",page:"practitioners"===x?f:1,...C});if(console.log(`Practitioner raw response data length for specialty ${h}: ${e?.data?.length||0}`),e?.data&&e.data.length>0){let t=e.data[0];console.log(`First raw practitioner item structure for specialty ${h}:`,JSON.stringify({id:t.id,hasAttributes:!!t.attributes,attributesType:t.attributes?typeof t.attributes:"N/A",keys:Object.keys(t),attributeKeys:t.attributes?Object.keys(t.attributes):[]},null,2))}U=(e?.data||[]).map(e=>{let t=e.attributes||e;return e?.id&&!t.id&&(t.id=e.id),t.id&&t.name||console.warn(`Practitioner data missing id or name for specialty ${h}. ID: ${t.id}, Name: ${t.name}. Full item:`,JSON.stringify(e)),(console.log("Raw practitioner data structure:",JSON.stringify({hasData:!!t,id:t?.id,name:t?.name,keys:t?Object.keys(t):[]},null,2)),t)?t.id&&t.name?{id:String(t.id),name:t.name,slug:t.slug||`practitioner-${t.id}`,title:t.title,qualifications:t.qualifications,profilePicture:w(t.profilePicture),isVerified:t.isVerified||!1,bio:t.bio}:(console.warn(`Skipping invalid practitioner data: ID ${t?.id}`),null):(console.warn("Skipping practitioner due to missing data."),null)}).filter(e=>null!==e),console.log(`Transformed practitioners array length for specialty ${h}: ${U.length}`),k=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching practitioners for specialty ${h}:`,e)}A="clinics"===x?q:k;let R=(b?.attributes||b||{}).seo,D=null;if(R?.structuredData){if("string"==typeof R.structuredData)D=R.structuredData;else if("object"==typeof R.structuredData)try{D=JSON.stringify(R.structuredData)}catch(e){console.error("Failed to stringify structuredData object:",e)}}return D||(D=JSON.stringify({"@context":"https://schema.org","@type":"MedicalSpecialty",name:j,description:v,url:`${process.env.NEXT_PUBLIC_SITE_URL||""}/specialities/${h}`,mainEntityOfPage:{"@type":"WebPage","@id":`${process.env.NEXT_PUBLIC_SITE_URL||""}/specialities/${h}`}})),(0,i.jsxs)(i.Fragment,{children:[D&&(0,i.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:D}}),(0,i.jsx)("div",{className:"bg-gray-100 py-3",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)(s(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,i.jsx)("span",{className:"mx-2",children:"/"}),(0,i.jsx)(s(),{href:"/specialities",className:"hover:text-emerald-600",children:"Specialties"}),(0,i.jsx)("span",{className:"mx-2",children:"/"}),(0,i.jsx)("span",{className:"text-gray-800",children:j})," "]})})}),(0,i.jsxs)("div",{className:"bg-emerald-600 text-white py-12 relative overflow-hidden",children:[S&&(0,i.jsx)("div",{className:"absolute inset-0 opacity-20 bg-cover bg-center",style:{backgroundImage:`url(${S})`}}),(0,i.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,i.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:j}),v&&(0,i.jsx)("p",{className:"text-lg max-w-3xl mb-4",children:v}),(0,i.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,i.jsxs)("span",{children:[I," Related Clinics"]}),(0,i.jsx)("span",{children:"•"}),(0,i.jsxs)("span",{children:[P," Related Practitioners"]})]})]})]}),(0,i.jsx)("div",{className:"bg-white shadow-sm sticky top-0 z-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)(d.default,{placeholder:`Search clinics/practitioners for ${j}...`,paramName:"query"})}),(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)(d.default,{placeholder:"City, state, or zip code",paramName:"location",icon:(0,i.jsx)(n.HzC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})})}),(0,i.jsx)("div",{children:(0,i.jsxs)("button",{className:"w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200",children:[(0,i.jsx)(n.K7R,{}),(0,i.jsx)("span",{children:"Filters"})]})})]})})}),(0,i.jsx)("div",{className:"py-8 bg-gray-50",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsx)(u.default,{slug:y.slug,pageType:"specialities",clinicCount:I,practitionerCount:P,initialTab:x}),(0,i.jsx)(g.default,{clinics:_,practitioners:U,totalPages:A,initialTab:x})]})}),(0,i.jsx)(c.A,{})]})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65996:(e,t,a)=>{a.r(t),a.d(t,{GlobalError:()=>s.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>o});var i=a(65239),r=a(48088),s=a(31369),n=a(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let o={children:["",{children:["specialities",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,10448)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\specialities\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\specialities\\[slug]\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/specialities/[slug]/page",pathname:"/specialities/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[7719,1330,3376,6391,2975,255,8446,270,7424,7685],()=>a(65996));module.exports=i})();