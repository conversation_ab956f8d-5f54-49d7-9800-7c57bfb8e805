(()=>{var e={};e.id=977,e.ids=[977],e.modules={1573:(e,t,i)=>{"use strict";let n=i(27910).Stream,a=i(29564),r=i(55511),o=i(89581);class s extends n{constructor(e,t){if(super(),this.options=e||{},e&&e.serviceClient){if(!e.privateKey||!e.user)return void setImmediate(()=>this.emit("error",Error('Options "privateKey" and "user" are required for service account!')));let t=Math.min(Math.max(Number(this.options.serviceRequestTimeout)||0,0),3600);this.options.serviceRequestTimeout=t||300}if(this.logger=o.getLogger({logger:t},{component:this.options.component||"OAuth2"}),this.provisionCallback="function"==typeof this.options.provisionCallback&&this.options.provisionCallback,this.options.accessUrl=this.options.accessUrl||"https://accounts.google.com/o/oauth2/token",this.options.customHeaders=this.options.customHeaders||{},this.options.customParams=this.options.customParams||{},this.accessToken=this.options.accessToken||!1,this.options.expires&&Number(this.options.expires))this.expires=this.options.expires;else{let e=Math.max(Number(this.options.timeout)||0,0);this.expires=e&&Date.now()+1e3*e||0}}getToken(e,t){if(!e&&this.accessToken&&(!this.expires||this.expires>Date.now()))return t(null,this.accessToken);let i=(...e)=>{e[0]?this.logger.error({err:e[0],tnx:"OAUTH2",user:this.options.user,action:"renew"},"Failed generating new Access Token for %s",this.options.user):this.logger.info({tnx:"OAUTH2",user:this.options.user,action:"renew"},"Generated new Access Token for %s",this.options.user),t(...e)};this.provisionCallback?this.provisionCallback(this.options.user,!!e,(e,t,n)=>{!e&&t&&(this.accessToken=t,this.expires=n||0),i(e,t)}):this.generateToken(i)}updateToken(e,t){this.accessToken=e,t=Math.max(Number(t)||0,0),this.expires=t&&Date.now()+1e3*t||0,this.emit("token",{user:this.options.user,accessToken:e||"",expires:this.expires})}generateToken(e){let t,i;if(this.options.serviceClient){let n,a=Math.floor(Date.now()/1e3),r={iss:this.options.serviceClient,scope:this.options.scope||"https://mail.google.com/",sub:this.options.user,aud:this.options.accessUrl,iat:a,exp:a+this.options.serviceRequestTimeout};try{n=this.jwtSignRS256(r)}catch(t){return e(Error("Can't generate token. Check your auth options"))}t={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:n},i={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:r}}else{if(!this.options.refreshToken)return e(Error("Can't create new access token for user"));t={client_id:this.options.clientId||"",client_secret:this.options.clientSecret||"",refresh_token:this.options.refreshToken,grant_type:"refresh_token"},i={client_id:this.options.clientId||"",client_secret:(this.options.clientSecret||"").substr(0,6)+"...",refresh_token:(this.options.refreshToken||"").substr(0,6)+"...",grant_type:"refresh_token"}}Object.keys(this.options.customParams).forEach(e=>{t[e]=this.options.customParams[e],i[e]=this.options.customParams[e]}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"generate"},"Requesting token using: %s",JSON.stringify(i)),this.postRequest(this.options.accessUrl,t,this.options,(t,i)=>{let n;if(t)return e(t);try{n=JSON.parse(i.toString())}catch(t){return e(t)}if(!n||"object"!=typeof n)return this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",(i||"").toString()),e(Error("Invalid authentication response"));let a={};if(Object.keys(n).forEach(e=>{"access_token"!==e?a[e]=n[e]:a[e]=(n[e]||"").toString().substr(0,6)+"..."}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",JSON.stringify(a)),n.error){let t=n.error;return n.error_description&&(t+=": "+n.error_description),n.error_uri&&(t+=" ("+n.error_uri+")"),e(Error(t))}return n.access_token?(this.updateToken(n.access_token,n.expires_in),e(null,this.accessToken)):e(Error("No access token"))})}buildXOAuth2Token(e){let t=["user="+(this.options.user||""),"auth=Bearer "+(e||this.accessToken),"",""];return Buffer.from(t.join("\x01"),"utf-8").toString("base64")}postRequest(e,t,i,n){let r=!1,o=[],s=0,l=a(e,{method:"post",headers:i.customHeaders,body:t,allowErrorResponse:!0});l.on("readable",()=>{let e;for(;null!==(e=l.read());)o.push(e),s+=e.length}),l.once("error",e=>{if(!r)return r=!0,n(e)}),l.once("end",()=>{if(!r)return r=!0,n(null,Buffer.concat(o,s))})}toBase64URL(e){return"string"==typeof e&&(e=Buffer.from(e)),e.toString("base64").replace(/[=]+/g,"").replace(/\+/g,"-").replace(/\//g,"_")}jwtSignRS256(e){e=['{"alg":"RS256","typ":"JWT"}',JSON.stringify(e)].map(e=>this.toBase64URL(e)).join(".");let t=r.createSign("RSA-SHA256").update(e).sign(this.options.privateKey);return e+"."+this.toBase64URL(t)}}e.exports=s},2720:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{AppRenderSpan:function(){return l},AppRouteRouteHandlersSpan:function(){return d},BaseServerSpan:function(){return i},LoadComponentsSpan:function(){return n},LogSpanAllowList:function(){return f},MiddlewareSpan:function(){return m},NextNodeServerSpan:function(){return r},NextServerSpan:function(){return a},NextVanillaSpanAllowlist:function(){return h},NodeSpan:function(){return p},RenderSpan:function(){return s},ResolveMetadataSpan:function(){return u},RouterSpan:function(){return c},StartServerSpan:function(){return o}});var i=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(i||{}),n=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(n||{}),a=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(a||{}),r=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(r||{}),o=function(e){return e.startServer="startServer.startServer",e}(o||{}),s=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(s||{}),l=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(l||{}),c=function(e){return e.executeRoute="Router.executeRoute",e}(c||{}),p=function(e){return e.runHandler="Node.runHandler",e}(p||{}),d=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(d||{}),u=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(u||{}),m=function(e){return e.execute="Middleware.execute",e}(m||{});let h=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],f=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3350:e=>{"use strict";e.exports=JSON.parse('{"126":{"host":"smtp.126.com","port":465,"secure":true},"163":{"host":"smtp.163.com","port":465,"secure":true},"1und1":{"host":"smtp.1und1.de","port":465,"secure":true,"authMethod":"LOGIN"},"Aliyun":{"domains":["aliyun.com"],"host":"smtp.aliyun.com","port":465,"secure":true},"AOL":{"domains":["aol.com"],"host":"smtp.aol.com","port":587},"Bluewin":{"host":"smtpauths.bluewin.ch","domains":["bluewin.ch"],"port":465},"DebugMail":{"host":"debugmail.io","port":25},"DynectEmail":{"aliases":["Dynect"],"host":"smtp.dynect.net","port":25},"Ethereal":{"aliases":["ethereal.email"],"host":"smtp.ethereal.email","port":587},"FastMail":{"domains":["fastmail.fm"],"host":"smtp.fastmail.com","port":465,"secure":true},"Forward Email":{"aliases":["FE","ForwardEmail"],"domains":["forwardemail.net"],"host":"smtp.forwardemail.net","port":465,"secure":true},"Feishu Mail":{"aliases":["Feishu","FeishuMail"],"domains":["www.feishu.cn"],"host":"smtp.feishu.cn","port":465,"secure":true},"GandiMail":{"aliases":["Gandi","Gandi Mail"],"host":"mail.gandi.net","port":587},"Gmail":{"aliases":["Google Mail"],"domains":["gmail.com","googlemail.com"],"host":"smtp.gmail.com","port":465,"secure":true},"Godaddy":{"host":"smtpout.secureserver.net","port":25},"GodaddyAsia":{"host":"smtp.asia.secureserver.net","port":25},"GodaddyEurope":{"host":"smtp.europe.secureserver.net","port":25},"hot.ee":{"host":"mail.hot.ee"},"Hotmail":{"aliases":["Outlook","Outlook.com","Hotmail.com"],"domains":["hotmail.com","outlook.com"],"host":"smtp-mail.outlook.com","port":587},"iCloud":{"aliases":["Me","Mac"],"domains":["me.com","mac.com"],"host":"smtp.mail.me.com","port":587},"Infomaniak":{"host":"mail.infomaniak.com","domains":["ik.me","ikmail.com","etik.com"],"port":587},"Loopia":{"host":"mailcluster.loopia.se","port":465},"mail.ee":{"host":"smtp.mail.ee"},"Mail.ru":{"host":"smtp.mail.ru","port":465,"secure":true},"Mailcatch.app":{"host":"sandbox-smtp.mailcatch.app","port":2525},"Maildev":{"port":1025,"ignoreTLS":true},"Mailgun":{"host":"smtp.mailgun.org","port":465,"secure":true},"Mailjet":{"host":"in.mailjet.com","port":587},"Mailosaur":{"host":"mailosaur.io","port":25},"Mailtrap":{"host":"live.smtp.mailtrap.io","port":587},"Mandrill":{"host":"smtp.mandrillapp.com","port":587},"Naver":{"host":"smtp.naver.com","port":587},"One":{"host":"send.one.com","port":465,"secure":true},"OpenMailBox":{"aliases":["OMB","openmailbox.org"],"host":"smtp.openmailbox.org","port":465,"secure":true},"Outlook365":{"host":"smtp.office365.com","port":587,"secure":false},"OhMySMTP":{"host":"smtp.ohmysmtp.com","port":587,"secure":false},"Postmark":{"aliases":["PostmarkApp"],"host":"smtp.postmarkapp.com","port":2525},"Proton":{"aliases":["ProtonMail","Proton.me","Protonmail.com","Protonmail.ch"],"domains":["proton.me","protonmail.com","pm.me","protonmail.ch"],"host":"smtp.protonmail.ch","port":587,"requireTLS":true},"qiye.aliyun":{"host":"smtp.mxhichina.com","port":"465","secure":true},"QQ":{"domains":["qq.com"],"host":"smtp.qq.com","port":465,"secure":true},"QQex":{"aliases":["QQ Enterprise"],"domains":["exmail.qq.com"],"host":"smtp.exmail.qq.com","port":465,"secure":true},"SendCloud":{"host":"smtp.sendcloud.net","port":2525},"SendGrid":{"host":"smtp.sendgrid.net","port":587},"SendinBlue":{"aliases":["Brevo"],"host":"smtp-relay.brevo.com","port":587},"SendPulse":{"host":"smtp-pulse.com","port":465,"secure":true},"SES":{"host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-US-EAST-1":{"host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-US-WEST-2":{"host":"email-smtp.us-west-2.amazonaws.com","port":465,"secure":true},"SES-EU-WEST-1":{"host":"email-smtp.eu-west-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTH-1":{"host":"email-smtp.ap-south-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-1":{"host":"email-smtp.ap-northeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-2":{"host":"email-smtp.ap-northeast-2.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-3":{"host":"email-smtp.ap-northeast-3.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-1":{"host":"email-smtp.ap-southeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-2":{"host":"email-smtp.ap-southeast-2.amazonaws.com","port":465,"secure":true},"Seznam":{"aliases":["Seznam Email"],"domains":["seznam.cz","email.cz","post.cz","spoluzaci.cz"],"host":"smtp.seznam.cz","port":465,"secure":true},"Sparkpost":{"aliases":["SparkPost","SparkPost Mail"],"domains":["sparkpost.com"],"host":"smtp.sparkpostmail.com","port":587,"secure":false},"Tipimail":{"host":"smtp.tipimail.com","port":587},"Yahoo":{"domains":["yahoo.com"],"host":"smtp.mail.yahoo.com","port":465,"secure":true},"Yandex":{"domains":["yandex.ru"],"host":"smtp.yandex.ru","port":465,"secure":true},"Zoho":{"host":"smtp.zoho.com","port":465,"secure":true,"authMethod":"LOGIN"}}')},5328:(e,t,i)=>{"use strict";let n=i(27910).Transform;class a extends n{constructor(){super(),this.lastByte=!1}_transform(e,t,i){e.length&&(this.lastByte=e[e.length-1]),this.push(e),i()}_flush(e){return 10===this.lastByte||(13===this.lastByte?this.push(Buffer.from("\n")):this.push(Buffer.from("\r\n"))),e()}}e.exports=a},5872:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>d});var n=i(60687),a=i(17019),r=i(51215),o=i(43210),s=i(6475);let l=(0,s.createServerReference)("6092811ebc6b79ca629690c3d3930d04724eef3df3",s.callServer,void 0,s.findSourceMapURL,"sendContactMessage"),c={message:"",success:!1,errors:void 0};function p(){let{pending:e}=(0,r.useFormStatus)();return(0,n.jsxs)("button",{type:"submit","aria-disabled":e,disabled:e,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center disabled:opacity-70 disabled:cursor-not-allowed",children:[(0,n.jsx)(a.kGk,{className:"mr-2"}),e?"Sending...":"Send Message"]})}function d(){let[e,t]=(0,r.useFormState)(l,c),i=(0,o.useRef)(null),s={email:"<EMAIL>"};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Contact Us"}),(0,n.jsx)("p",{className:"text-lg max-w-3xl mx-auto",children:"Have questions about natural healing options? We're here to help you find the right resources for your wellness journey."})]})}),(0,n.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-12",children:[(0,n.jsxs)("div",{className:"w-full max-w-xl",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:"Send Us a Message"}),e.message&&!e.success&&(0,n.jsx)("div",{className:"mb-4 p-3 rounded-md bg-red-100 text-red-700 text-sm",children:e.message}),e.message&&e.success&&(0,n.jsx)("div",{className:"mb-4 p-3 rounded-md bg-green-100 text-green-700 text-sm",children:e.message}),(0,n.jsxs)("form",{ref:i,action:t,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Name"}),(0,n.jsx)("input",{type:"text",id:"name",name:"name",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"John Doe"}),e.errors?.name&&(0,n.jsx)("p",{className:"mt-1 text-xs text-red-600",children:e.errors.name.join(", ")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,n.jsx)("input",{type:"email",id:"email",name:"email",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"<EMAIL>"}),e.errors?.email&&(0,n.jsx)("p",{className:"mt-1 text-xs text-red-600",children:e.errors.email.join(", ")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-1",children:"Subject"}),(0,n.jsx)("input",{type:"text",id:"subject",name:"subject",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"How can we help you?"}),e.errors?.subject&&(0,n.jsx)("p",{className:"mt-1 text-xs text-red-600",children:e.errors.subject.join(", ")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,n.jsx)("textarea",{id:"message",name:"message",rows:6,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"Your message here..."}),e.errors?.message&&(0,n.jsx)("p",{className:"mt-1 text-xs text-red-600",children:e.errors.message.join(", ")})]}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(p,{})})]})]}),(0,n.jsx)("div",{className:"w-full max-w-xl text-center",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,n.jsx)("p",{className:"text-gray-700 mb-4",children:"For direct inquiries, you can also reach us at:"}),(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)(a.pHD,{className:"h-6 w-6 text-emerald-600"})})}),(0,n.jsx)("div",{className:"ml-4",children:(0,n.jsx)("p",{className:"mt-0 text-gray-600 text-lg",children:(0,n.jsx)("a",{href:`mailto:${process.env.NEXT_PUBLIC_CONTACT_EMAIL||s.email}`,className:"hover:text-emerald-600 font-medium",children:process.env.NEXT_PUBLIC_CONTACT_EMAIL||s.email})})})]})})]})})]})})]})}},6280:(e,t,i)=>{Promise.resolve().then(i.bind(i,43839))},6475:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return r},findSourceMapURL:function(){return a.findSourceMapURL}});let n=i(11264),a=i(11448),r=i(19357).createServerReference},7153:(e,t,i)=>{"use strict";e.exports=i(65239).vendored["react-rsc"].React},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12501:(e,t,i)=>{"use strict";let n=i(91423),a=i(94735).EventEmitter,r=i(91645),o=i(34631),s=i(21820),l=i(55511),c=i(38424),p=i(27910).PassThrough,d=i(89581);class u extends a{constructor(e){super(e),this.id=l.randomBytes(8).toString("base64").replace(/\W/g,""),this.stage="init",this.options=e||{},this.secureConnection=!!this.options.secure,this.alreadySecured=!!this.options.secured,this.port=Number(this.options.port)||(this.secureConnection?465:587),this.host=this.options.host||"localhost",this.servername=this.options.servername?this.options.servername:!r.isIP(this.host)&&this.host,this.allowInternalNetworkInterfaces=this.options.allowInternalNetworkInterfaces||!1,void 0===this.options.secure&&465===this.port&&(this.secureConnection=!0),this.name=this.options.name||this._getHostname(),this.logger=d.getLogger(this.options,{component:this.options.component||"smtp-connection",sid:this.id}),this.customAuth=new Map,Object.keys(this.options.customAuth||{}).forEach(e=>{let t=(e||"").toString().trim().toUpperCase();t&&this.customAuth.set(t,this.options.customAuth[e])}),this.version=n.version,this.authenticated=!1,this.destroyed=!1,this.secure=!!this.secureConnection,this._remainder="",this._responseQueue=[],this.lastServerResponse=!1,this._socket=!1,this._supportedAuth=[],this.allowsAuth=!1,this._envelope=!1,this._supportedExtensions=[],this._maxAllowedSize=0,this._responseActions=[],this._recipientQueue=[],this._greetingTimeout=!1,this._connectionTimeout=!1,this._destroyed=!1,this._closing=!1,this._onSocketData=e=>this._onData(e),this._onSocketError=e=>this._onError(e,"ESOCKET",!1,"CONN"),this._onSocketClose=()=>this._onClose(),this._onSocketEnd=()=>this._onEnd(),this._onSocketTimeout=()=>this._onTimeout()}connect(e){if("function"==typeof e){this.once("connect",()=>{this.logger.debug({tnx:"smtp"},"SMTP handshake finished"),e()});let t=this._isDestroyedMessage("connect");if(t)return e(this._formatError(t,"ECONNECTION",!1,"CONN"))}let t={port:this.port,host:this.host,allowInternalNetworkInterfaces:this.allowInternalNetworkInterfaces,timeout:this.options.dnsTimeout||3e4};this.options.localAddress&&(t.localAddress=this.options.localAddress);let i=()=>{this._connectionTimeout=setTimeout(()=>{this._onError("Connection timeout","ETIMEDOUT",!1,"CONN")},this.options.connectionTimeout||12e4),this._socket.on("error",this._onSocketError)};if(this.options.connection){this._socket=this.options.connection,i(),this.secureConnection&&!this.alreadySecured?setImmediate(()=>this._upgradeConnection(e=>{if(e)return void this._onError(Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"CONN");this._onConnect()})):setImmediate(()=>this._onConnect());return}return this.options.socket?(this._socket=this.options.socket,d.resolveHostname(t,(e,n)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:n.host,cached:!!n.cached},"Resolved %s as %s [cache %s]",t.host,n.host,n.cached?"hit":"miss"),Object.keys(n).forEach(e=>{"_"!==e.charAt(0)&&n[e]&&(t[e]=n[e])});try{this._socket.connect(this.port,this.host,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):this.secureConnection?(this.options.tls&&Object.keys(this.options.tls).forEach(e=>{t[e]=this.options.tls[e]}),this.servername&&!t.servername&&(t.servername=this.servername),d.resolveHostname(t,(e,n)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:n.host,cached:!!n.cached},"Resolved %s as %s [cache %s]",t.host,n.host,n.cached?"hit":"miss"),Object.keys(n).forEach(e=>{"_"!==e.charAt(0)&&n[e]&&(t[e]=n[e])});try{this._socket=o.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):d.resolveHostname(t,(e,n)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:n.host,cached:!!n.cached},"Resolved %s as %s [cache %s]",t.host,n.host,n.cached?"hit":"miss"),Object.keys(n).forEach(e=>{"_"!==e.charAt(0)&&n[e]&&(t[e]=n[e])});try{this._socket=r.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})}quit(){this._sendCommand("QUIT"),this._responseActions.push(this.close)}close(){if(clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._responseActions=[],this._closing)return;this._closing=!0;let e="end";"init"===this.stage&&(e="destroy"),this.logger.debug({tnx:"smtp"},'Closing connection to the server using "%s"',e);let t=this._socket&&this._socket.socket||this._socket;if(t&&!t.destroyed)try{t[e]()}catch(e){}this._destroy()}login(e,t){let i=this._isDestroyedMessage("login");if(i)return t(this._formatError(i,"ECONNECTION",!1,"API"));if(this._auth=e||{},this._authMethod=(this._auth.method||"").toString().trim().toUpperCase()||!1,this._authMethod||!this._auth.oauth2||this._auth.credentials?this._authMethod&&("XOAUTH2"!==this._authMethod||this._auth.oauth2)||(this._authMethod=(this._supportedAuth[0]||"PLAIN").toUpperCase().trim()):this._authMethod="XOAUTH2","XOAUTH2"!==this._authMethod&&(!this._auth.credentials||!this._auth.credentials.user||!this._auth.credentials.pass))if(!(this._auth.user&&this._auth.pass||this.customAuth.has(this._authMethod)))return t(this._formatError('Missing credentials for "'+this._authMethod+'"',"EAUTH",!1,"API"));else this._auth.credentials={user:this._auth.user,pass:this._auth.pass,options:this._auth.options};if(this.customAuth.has(this._authMethod)){let e,i=this.customAuth.get(this._authMethod),n=!1,a=()=>{n||(n=!0,this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0))},r=i=>{n||(n=!0,t(this._formatError(i,"EAUTH",e,"AUTH "+this._authMethod)))},o=i({auth:this._auth,method:this._authMethod,extensions:[].concat(this._supportedExtensions),authMethods:[].concat(this._supportedAuth),maxAllowedSize:this._maxAllowedSize||!1,sendCommand:(t,i)=>{let n;return i||(n=new Promise((e,t)=>{i=d.callbackPromise(e,t)})),this._responseActions.push(n=>{e=n;let a=n.match(/^(\d+)(?:\s(\d+\.\d+\.\d+))?\s/),r={command:t,response:n};a?(r.status=Number(a[1])||0,a[2]&&(r.code=a[2]),r.text=n.substr(a[0].length)):(r.text=n,r.status=0),i(null,r)}),setImmediate(()=>this._sendCommand(t)),n},resolve:a,reject:r});o&&"function"==typeof o.catch&&o.then(a).catch(r);return}switch(this._authMethod){case"XOAUTH2":this._handleXOauth2Token(!1,t);return;case"LOGIN":this._responseActions.push(e=>{this._actionAUTH_LOGIN_USER(e,t)}),this._sendCommand("AUTH LOGIN");return;case"PLAIN":this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),this._sendCommand("AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0"+this._auth.credentials.pass,"utf-8").toString("base64"),"AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0/* secret */","utf-8").toString("base64"));return;case"CRAM-MD5":this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5(e,t)}),this._sendCommand("AUTH CRAM-MD5");return}return t(this._formatError('Unknown authentication method "'+this._authMethod+'"',"EAUTH",!1,"API"))}send(e,t,i){if(!t)return i(this._formatError("Empty message","EMESSAGE",!1,"API"));let n=this._isDestroyedMessage("send message");if(n)return i(this._formatError(n,"ECONNECTION",!1,"API"));if(this._maxAllowedSize&&e.size>this._maxAllowedSize)return setImmediate(()=>{i(this._formatError("Message size larger than allowed "+this._maxAllowedSize,"EMESSAGE",!1,"MAIL FROM"))});let a=!1,r=function(){a||(a=!0,i(...arguments))};"function"==typeof t.on&&t.on("error",e=>r(this._formatError(e,"ESTREAM",!1,"API")));let o=Date.now();this._setEnvelope(e,(e,i)=>{if(e){let i=new p;return"function"==typeof t.pipe?t.pipe(i):(i.write(t),i.end()),r(e)}let n=Date.now(),a=this._createSendStream((e,t)=>e?r(e):(i.envelopeTime=n-o,i.messageTime=Date.now()-n,i.messageSize=a.outByteCount,i.response=t,r(null,i)));"function"==typeof t.pipe?t.pipe(a):(a.write(t),a.end())})}reset(e){this._sendCommand("RSET"),this._responseActions.push(t=>"2"!==t.charAt(0)?e(this._formatError("Could not reset session state. response="+t,"EPROTOCOL",t,"RSET")):(this._envelope=!1,e(null,!0)))}_onConnect(){if(clearTimeout(this._connectionTimeout),this.logger.info({tnx:"network",localAddress:this._socket.localAddress,localPort:this._socket.localPort,remoteAddress:this._socket.remoteAddress,remotePort:this._socket.remotePort},"%s established to %s:%s",this.secure?"Secure connection":"Connection",this._socket.remoteAddress,this._socket.remotePort),this._destroyed)return void this.close();this.stage="connected",this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout),this._socket.removeListener("close",this._onSocketClose),this._socket.removeListener("end",this._onSocketEnd),this._socket.on("data",this._onSocketData),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),this._greetingTimeout=setTimeout(()=>{this._socket&&!this._destroyed&&this._responseActions[0]===this._actionGreeting&&this._onError("Greeting never received","ETIMEDOUT",!1,"CONN")},this.options.greetingTimeout||3e4),this._responseActions.push(this._actionGreeting),this._socket.resume()}_onData(e){let t;if(this._destroyed||!e||!e.length)return;let i=(e||"").toString("binary"),n=(this._remainder+i).split(/\r?\n/);this._remainder=n.pop();for(let e=0,i=n.length;e<i;e++){if(this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))){this._responseQueue[this._responseQueue.length-1]+="\n"+n[e];continue}this._responseQueue.push(n[e])}this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))||this._processResponse()}_onError(e,t,i,n){clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._destroyed||(e=this._formatError(e,t,i,n),this.logger.error(i,e.message),this.emit("error",e),this.close())}_formatError(e,t,i,n){let a;a=/Error\]$/i.test(Object.prototype.toString.call(e))?e:Error(e),t&&"Error"!==t&&(a.code=t),i&&(a.response=i,a.message+=": "+i);let r="string"==typeof i&&Number((i.match(/^\d+/)||[])[0])||!1;return r&&(a.responseCode=r),n&&(a.command=n),a}_onClose(){let e=!1;return(this._remainder&&this._remainder.trim()&&((this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},this._remainder.replace(/\r?\n$/,"")),this.lastServerResponse=e=this._remainder.trim()),this.logger.info({tnx:"network"},"Connection closed"),this.upgrading&&!this._destroyed)?this._onError(Error("Connection closed unexpectedly"),"ETLS",e,"CONN"):![this._actionGreeting,this.close].includes(this._responseActions[0])&&!this._destroyed||/^[45]\d{2}\b/.test(e)?this._onError(Error("Connection closed unexpectedly"),"ECONNECTION",e,"CONN"):void this._destroy()}_onEnd(){this._socket&&!this._socket.destroyed&&this._socket.destroy()}_onTimeout(){return this._onError(Error("Timeout"),"ETIMEDOUT",!1,"CONN")}_destroy(){this._destroyed||(this._destroyed=!0,this.emit("end"))}_upgradeConnection(e){this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout);let t=this._socket,i={socket:this._socket,host:this.host};Object.keys(this.options.tls||{}).forEach(e=>{i[e]=this.options.tls[e]}),this.servername&&!i.servername&&(i.servername=this.servername),this.upgrading=!0;try{this._socket=o.connect(i,()=>(this.secure=!0,this.upgrading=!1,this._socket.on("data",this._onSocketData),t.removeListener("close",this._onSocketClose),t.removeListener("end",this._onSocketEnd),e(null,!0)))}catch(t){return e(t)}this._socket.on("error",this._onSocketError),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),t.resume()}_processResponse(){if(!this._responseQueue.length)return!1;let e=this.lastServerResponse=(this._responseQueue.shift()||"").toString();if(/^\d+-/.test(e.split("\n").pop()))return;(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},e.replace(/\r?\n$/,"")),e.trim()||setImmediate(()=>this._processResponse());let t=this._responseActions.shift();if("function"!=typeof t)return this._onError(Error("Unexpected Response"),"EPROTOCOL",e,"CONN");t.call(this,e),setImmediate(()=>this._processResponse())}_sendCommand(e,t){if(!this._destroyed){if(this._socket.destroyed)return this.close();(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"client"},(t||e||"").toString().replace(/\r?\n$/,"")),this._socket.write(Buffer.from(e+"\r\n","utf-8"))}}_setEnvelope(e,t){let i=[],n=!1;if(this._envelope=e||{},this._envelope.from=(this._envelope.from&&this._envelope.from.address||this._envelope.from||"").toString().trim(),this._envelope.to=[].concat(this._envelope.to||[]).map(e=>(e&&e.address||e||"").toString().trim()),!this._envelope.to.length)return t(this._formatError("No recipients defined","EENVELOPE",!1,"API"));if(this._envelope.from&&/[\r\n<>]/.test(this._envelope.from))return t(this._formatError("Invalid sender "+JSON.stringify(this._envelope.from),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.from)&&(n=!0);for(let e=0,i=this._envelope.to.length;e<i;e++){if(!this._envelope.to[e]||/[\r\n<>]/.test(this._envelope.to[e]))return t(this._formatError("Invalid recipient "+JSON.stringify(this._envelope.to[e]),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.to[e])&&(n=!0)}if(this._envelope.rcptQueue=JSON.parse(JSON.stringify(this._envelope.to||[])),this._envelope.rejected=[],this._envelope.rejectedErrors=[],this._envelope.accepted=[],this._envelope.dsn)try{this._envelope.dsn=this._setDsnEnvelope(this._envelope.dsn)}catch(e){return t(this._formatError("Invalid DSN "+e.message,"EENVELOPE",!1,"API"))}this._responseActions.push(e=>{this._actionMAIL(e,t)}),n&&this._supportedExtensions.includes("SMTPUTF8")&&(i.push("SMTPUTF8"),this._usingSmtpUtf8=!0),this._envelope.use8BitMime&&this._supportedExtensions.includes("8BITMIME")&&(i.push("BODY=8BITMIME"),this._using8BitMime=!0),this._envelope.size&&this._supportedExtensions.includes("SIZE")&&i.push("SIZE="+this._envelope.size),this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.ret&&i.push("RET="+d.encodeXText(this._envelope.dsn.ret)),this._envelope.dsn.envid&&i.push("ENVID="+d.encodeXText(this._envelope.dsn.envid))),this._sendCommand("MAIL FROM:<"+this._envelope.from+">"+(i.length?" "+i.join(" "):""))}_setDsnEnvelope(e){let t=(e.ret||e.return||"").toString().toUpperCase()||null;if(t)switch(t){case"HDRS":case"HEADERS":t="HDRS";break;case"FULL":case"BODY":t="FULL"}if(t&&!["FULL","HDRS"].includes(t))throw Error("ret: "+JSON.stringify(t));let i=(e.envid||e.id||"").toString()||null,n=e.notify||null;if(n){"string"==typeof n&&(n=n.split(","));let e=["NEVER","SUCCESS","FAILURE","DELAY"];if((n=n.map(e=>e.trim().toUpperCase())).filter(t=>!e.includes(t)).length||n.length>1&&n.includes("NEVER"))throw Error("notify: "+JSON.stringify(n.join(",")));n=n.join(",")}let a=(e.recipient||e.orcpt||"").toString()||null;return a&&0>a.indexOf(";")&&(a="rfc822;"+a),{ret:t,envid:i,notify:n,orcpt:a}}_getDsnRcptToArgs(){let e=[];return this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.notify&&e.push("NOTIFY="+d.encodeXText(this._envelope.dsn.notify)),this._envelope.dsn.orcpt&&e.push("ORCPT="+d.encodeXText(this._envelope.dsn.orcpt))),e.length?" "+e.join(" "):""}_createSendStream(e){let t,i=new c;return this.options.lmtp?this._envelope.accepted.forEach((t,i)=>{let n=i===this._envelope.accepted.length-1;this._responseActions.push(i=>{this._actionLMTPStream(t,n,i,e)})}):this._responseActions.push(t=>{this._actionSMTPStream(t,e)}),i.pipe(this._socket,{end:!1}),this.options.debug&&((t=new p).on("readable",()=>{let e;for(;e=t.read();)this.logger.debug({tnx:"message"},e.toString("binary").replace(/\r?\n$/,""))}),i.pipe(t)),i.once("end",()=>{this.logger.info({tnx:"message",inByteCount:i.inByteCount,outByteCount:i.outByteCount},"<%s bytes encoded mime message (source size %s bytes)>",i.outByteCount,i.inByteCount)}),i}_actionGreeting(e){if(clearTimeout(this._greetingTimeout),"220"!==e.substr(0,3))return void this._onError(Error("Invalid greeting. response="+e),"EPROTOCOL",e,"CONN");this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name))}_actionLHLO(e){if("2"!==e.charAt(0))return void this._onError(Error("Invalid LHLO. response="+e),"EPROTOCOL",e,"LHLO");this._actionEHLO(e)}_actionEHLO(e){let t;if("421"===e.substr(0,3))return void this._onError(Error("Server terminates connection. response="+e),"ECONNECTION",e,"EHLO");if("2"!==e.charAt(0))return this.options.requireTLS?void this._onError(Error("EHLO failed but HELO does not support required STARTTLS. response="+e),"ECONNECTION",e,"EHLO"):(this._responseActions.push(this._actionHELO),void this._sendCommand("HELO "+this.name));if(this._ehloLines=e.split(/\r?\n/).map(e=>e.replace(/^\d+[ -]/,"").trim()).filter(e=>e).slice(1),!this.secure&&!this.options.ignoreTLS&&(/[ -]STARTTLS\b/im.test(e)||this.options.requireTLS)){this._sendCommand("STARTTLS"),this._responseActions.push(this._actionSTARTTLS);return}/[ -]SMTPUTF8\b/im.test(e)&&this._supportedExtensions.push("SMTPUTF8"),/[ -]DSN\b/im.test(e)&&this._supportedExtensions.push("DSN"),/[ -]8BITMIME\b/im.test(e)&&this._supportedExtensions.push("8BITMIME"),/[ -]PIPELINING\b/im.test(e)&&this._supportedExtensions.push("PIPELINING"),/[ -]AUTH\b/i.test(e)&&(this.allowsAuth=!0),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)PLAIN/i.test(e)&&this._supportedAuth.push("PLAIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)LOGIN/i.test(e)&&this._supportedAuth.push("LOGIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)CRAM-MD5/i.test(e)&&this._supportedAuth.push("CRAM-MD5"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)XOAUTH2/i.test(e)&&this._supportedAuth.push("XOAUTH2"),(t=e.match(/[ -]SIZE(?:[ \t]+(\d+))?/im))&&(this._supportedExtensions.push("SIZE"),this._maxAllowedSize=Number(t[1])||0),this.emit("connect")}_actionHELO(e){if("2"!==e.charAt(0))return void this._onError(Error("Invalid HELO. response="+e),"EPROTOCOL",e,"HELO");this.allowsAuth=!0,this.emit("connect")}_actionSTARTTLS(e){if("2"!==e.charAt(0))return this.options.opportunisticTLS?(this.logger.info({tnx:"smtp"},"Failed STARTTLS upgrade, continuing unencrypted"),this.emit("connect")):void this._onError(Error("Error upgrading connection with STARTTLS"),"ETLS",e,"STARTTLS");this._upgradeConnection((e,t)=>{if(e)return void this._onError(Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"STARTTLS");this.logger.info({tnx:"smtp"},"Connection upgraded with STARTTLS"),t?this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name)):this.emit("connect")})}_actionAUTH_LOGIN_USER(e,t){if(!/^334[ -]/.test(e))return void t(this._formatError('Invalid login sequence while waiting for "334 VXNlcm5hbWU6"',"EAUTH",e,"AUTH LOGIN"));this._responseActions.push(e=>{this._actionAUTH_LOGIN_PASS(e,t)}),this._sendCommand(Buffer.from(this._auth.credentials.user+"","utf-8").toString("base64"))}_actionAUTH_CRAM_MD5(e,t){let i=e.match(/^334\s+(.+)$/),n="";if(!i)return t(this._formatError("Invalid login sequence while waiting for server challenge string","EAUTH",e,"AUTH CRAM-MD5"));n=i[1];let a=Buffer.from(n,"base64").toString("ascii"),r=l.createHmac("md5",this._auth.credentials.pass);r.update(a);let o=this._auth.credentials.user+" "+r.digest("hex");this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5_PASS(e,t)}),this._sendCommand(Buffer.from(o).toString("base64"),Buffer.from(this._auth.credentials.user+" /* secret */").toString("base64"))}_actionAUTH_CRAM_MD5_PASS(e,t){if(!e.match(/^235\s+/))return t(this._formatError('Invalid login sequence while waiting for "235"',"EAUTH",e,"AUTH CRAM-MD5"));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0)}_actionAUTH_LOGIN_PASS(e,t){if(!/^334[ -]/.test(e))return t(this._formatError('Invalid login sequence while waiting for "334 UGFzc3dvcmQ6"',"EAUTH",e,"AUTH LOGIN"));this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),this._sendCommand(Buffer.from((this._auth.credentials.pass||"").toString(),"utf-8").toString("base64"),Buffer.from("/* secret */","utf-8").toString("base64"))}_actionAUTHComplete(e,t,i){if(i||"function"!=typeof t||(i=t,t=!1),"334"===e.substr(0,3)){this._responseActions.push(e=>{t||"XOAUTH2"!==this._authMethod?this._actionAUTHComplete(e,!0,i):setImmediate(()=>this._handleXOauth2Token(!0,i))}),this._sendCommand("");return}if("2"!==e.charAt(0))return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),i(this._formatError("Invalid login","EAUTH",e,"AUTH "+this._authMethod));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,i(null,!0)}_actionMAIL(e,t){let i,n;if(2!==Number(e.charAt(0)))return i=this._usingSmtpUtf8&&/^550 /.test(e)&&/[\x80-\uFFFF]/.test(this._envelope.from)?"Internationalized mailbox name not allowed":"Mail command failed",t(this._formatError(i,"EENVELOPE",e,"MAIL FROM"));if(!this._envelope.rcptQueue.length)return t(this._formatError("Can't send mail - no recipients defined","EENVELOPE",!1,"API"));if(this._recipientQueue=[],this._supportedExtensions.includes("PIPELINING"))for(;this._envelope.rcptQueue.length;)n=this._envelope.rcptQueue.shift(),this._recipientQueue.push(n),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+n+">"+this._getDsnRcptToArgs());else n=this._envelope.rcptQueue.shift(),this._recipientQueue.push(n),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+n+">"+this._getDsnRcptToArgs())}_actionRCPT(e,t){let i,n,a=this._recipientQueue.shift();if(2!==Number(e.charAt(0))?(i=this._usingSmtpUtf8&&/^553 /.test(e)&&/[\x80-\uFFFF]/.test(a)?"Internationalized mailbox name not allowed":"Recipient command failed",this._envelope.rejected.push(a),(n=this._formatError(i,"EENVELOPE",e,"RCPT TO")).recipient=a,this._envelope.rejectedErrors.push(n)):this._envelope.accepted.push(a),this._envelope.rcptQueue.length||this._recipientQueue.length)this._envelope.rcptQueue.length&&(a=this._envelope.rcptQueue.shift(),this._recipientQueue.push(a),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+a+">"+this._getDsnRcptToArgs()));else{if(!(this._envelope.rejected.length<this._envelope.to.length))return(n=this._formatError("Can't send mail - all recipients were rejected","EENVELOPE",e,"RCPT TO")).rejected=this._envelope.rejected,n.rejectedErrors=this._envelope.rejectedErrors,t(n);this._responseActions.push(e=>{this._actionDATA(e,t)}),this._sendCommand("DATA")}}_actionDATA(e,t){if(!/^[23]/.test(e))return t(this._formatError("Data command failed","EENVELOPE",e,"DATA"));let i={accepted:this._envelope.accepted,rejected:this._envelope.rejected};this._ehloLines&&this._ehloLines.length&&(i.ehlo=this._ehloLines),this._envelope.rejectedErrors.length&&(i.rejectedErrors=this._envelope.rejectedErrors),t(null,i)}_actionSMTPStream(e,t){return 2!==Number(e.charAt(0))?t(this._formatError("Message failed","EMESSAGE",e,"DATA")):t(null,e)}_actionLMTPStream(e,t,i,n){let a;if(2!==Number(i.charAt(0))){(a=this._formatError("Message failed for recipient "+e,"EMESSAGE",i,"DATA")).recipient=e,this._envelope.rejected.push(e),this._envelope.rejectedErrors.push(a);for(let t=0,i=this._envelope.accepted.length;t<i;t++)this._envelope.accepted[t]===e&&this._envelope.accepted.splice(t,1)}if(t)return n(null,i)}_handleXOauth2Token(e,t){this._auth.oauth2.getToken(e,(i,n)=>{if(i)return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),t(this._formatError(i,"EAUTH",!1,"AUTH XOAUTH2"));this._responseActions.push(i=>{this._actionAUTHComplete(i,e,t)}),this._sendCommand("AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token(n),"AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token("/* secret */"))})}_isDestroyedMessage(e){if(this._destroyed)return"Cannot "+e+" - smtp connection is already destroyed.";if(this._socket){if(this._socket.destroyed)return"Cannot "+e+" - smtp connection socket is already destroyed.";if(!this._socket.writable)return"Cannot "+e+" - smtp connection socket is already half-closed."}}_getHostname(){let e;try{e=s.hostname()||""}catch(t){e="localhost"}return(!e||0>e.indexOf("."))&&(e="[127.0.0.1]"),e.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)&&(e="["+e+"]"),e}}e.exports=u},12894:(e,t,i)=>{"use strict";let n=i(27910).Transform;class a extends n{constructor(e){super(e),this.lastBytes=Buffer.alloc(4),this.headersParsed=!1,this.headerBytes=0,this.headerChunks=[],this.rawHeaders=!1,this.bodySize=0}updateLastBytes(e){let t=this.lastBytes.length,i=Math.min(e.length,t);for(let e=0,n=t-i;e<n;e++)this.lastBytes[e]=this.lastBytes[e+i];for(let n=1;n<=i;n++)this.lastBytes[t-n]=e[e.length-n]}checkHeaders(e){if(this.headersParsed)return!0;let t=this.lastBytes.length,i=0;this.curLinePos=0;for(let n=0,a=this.lastBytes.length+e.length;n<a;n++){let a;if(10===(n<t?this.lastBytes[n]:e[n-t])&&n){let a=n-1<t?this.lastBytes[n-1]:e[n-1-t],r=n>1&&(n-2<t?this.lastBytes[n-2]:e[n-2-t]);if(10===a||13===a&&10===r){this.headersParsed=!0,i=n-t+1,this.headerBytes+=i;break}}}if(this.headersParsed){if(this.headerChunks.push(e.slice(0,i)),this.rawHeaders=Buffer.concat(this.headerChunks,this.headerBytes),this.headerChunks=null,this.emit("headers",this.parseHeaders()),e.length-1>i){let t=e.slice(i);this.bodySize+=t.length,setImmediate(()=>this.push(t))}return!1}return this.headerBytes+=e.length,this.headerChunks.push(e),this.updateLastBytes(e),!1}_transform(e,t,i){let n;if(!e||!e.length)return i();"string"==typeof e&&(e=Buffer.from(e,t));try{n=this.checkHeaders(e)}catch(e){return i(e)}n&&(this.bodySize+=e.length,this.push(e)),setImmediate(i)}_flush(e){if(this.headerChunks){let e=Buffer.concat(this.headerChunks,this.headerBytes);this.bodySize+=e.length,this.push(e),this.headerChunks=null}e()}parseHeaders(){let e=(this.rawHeaders||"").toString().split(/\r?\n/);for(let t=e.length-1;t>0;t--)/^\s/.test(e[t])&&(e[t-1]+="\n"+e[t],e.splice(t,1));return e.filter(e=>e.trim()).map(e=>({key:e.substr(0,e.indexOf(":")).trim().toLowerCase(),line:e}))}}e.exports=a},13944:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DetachedPromise",{enumerable:!0,get:function(){return i}});class i{constructor(){let e,t;this.promise=new Promise((i,n)=>{e=i,t=n}),this.resolve=e,this.reject=t}}},18982:(e,t,i)=>{"use strict";let n=i(94735),a=i(89581),r=i(77400),o=i(44992),s=i(91551),l=i(25008),c=i(28354),p=i(79551),d=i(91423),u=i(44537),m=i(91645),h=i(37366),f=i(55511);class g extends n{constructor(e,t,i){super(),this.options=t||{},this._defaults=i||{},this._defaultPlugins={compile:[(...e)=>this._convertDataImages(...e)],stream:[]},this._userPlugins={compile:[],stream:[]},this.meta=new Map,this.dkim=!!this.options.dkim&&new s(this.options.dkim),this.transporter=e,this.transporter.mailer=this,this.logger=a.getLogger(this.options,{component:this.options.component||"mail"}),this.logger.debug({tnx:"create"},"Creating transport: %s",this.getVersionString()),"function"==typeof this.transporter.on&&(this.transporter.on("log",e=>{this.logger.debug({tnx:"transport"},"%s: %s",e.type,e.message)}),this.transporter.on("error",e=>{this.logger.error({err:e,tnx:"transport"},"Transport Error: %s",e.message),this.emit("error",e)}),this.transporter.on("idle",(...e)=>{this.emit("idle",...e)})),["close","isIdle","verify"].forEach(e=>{this[e]=(...t)=>"function"==typeof this.transporter[e]?("verify"===e&&"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1),this.transporter[e](...t)):(this.logger.warn({tnx:"transport",methodName:e},"Non existing method %s called for transport",e),!1)}),this.options.proxy&&"string"==typeof this.options.proxy&&this.setupProxy(this.options.proxy)}use(e,t){return e=(e||"").toString(),this._userPlugins.hasOwnProperty(e)?this._userPlugins[e].push(t):this._userPlugins[e]=[t],this}sendMail(e,t=null){let i;t||(i=new Promise((e,i)=>{t=a.callbackPromise(e,i)})),"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1);let n=new u(this,e);return this.logger.debug({tnx:"transport",name:this.transporter.name,version:this.transporter.version,action:"send"},"Sending mail using %s/%s",this.transporter.name,this.transporter.version),this._processPlugins("compile",n,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"compile"},"PluginCompile Error: %s",e.message),t(e);n.message=new o(n.data).compile(),n.setMailerHeader(),n.setPriorityHeaders(),n.setListHeaders(),this._processPlugins("stream",n,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"stream"},"PluginStream Error: %s",e.message),t(e);(n.data.dkim||this.dkim)&&n.message.processFunc(e=>{let t=n.data.dkim?new s(n.data.dkim):this.dkim;return this.logger.debug({tnx:"DKIM",messageId:n.message.messageId(),dkimDomains:t.keys.map(e=>e.keySelector+"."+e.domainName).join(", ")},"Signing outgoing message with %s keys",t.keys.length),t.sign(e,n.data._dkim)}),this.transporter.send(n,(...e)=>{e[0]&&this.logger.error({err:e[0],tnx:"transport",action:"send"},"Send Error: %s",e[0].message),t(...e)})})}),i}getVersionString(){return c.format("%s (%s; +%s; %s/%s)",d.name,d.version,d.homepage,this.transporter.name,this.transporter.version)}_processPlugins(e,t,i){if(e=(e||"").toString(),!this._userPlugins.hasOwnProperty(e))return i();let n=this._userPlugins[e]||[],a=this._defaultPlugins[e]||[];if(n.length&&this.logger.debug({tnx:"transaction",pluginCount:n.length,step:e},"Using %s plugins for %s",n.length,e),n.length+a.length===0)return i();let r=0,o="default",s=()=>{let e="default"===o?a:n;if(r>=e.length)if("default"!==o||!n.length)return i();else o="user",r=0,e=n;(0,e[r++])(t,e=>{if(e)return i(e);s()})};s()}setupProxy(e){let t=p.parse(e);this.getSocket=(e,i)=>{let n=t.protocol.replace(/:$/,"").toLowerCase();if(this.meta.has("proxy_handler_"+n))return this.meta.get("proxy_handler_"+n)(t,e,i);switch(n){case"http":case"https":l(t.href,e.port,e.host,(e,t)=>e?i(e):i(null,{connection:t}));return;case"socks":case"socks5":case"socks4":case"socks4a":{if(!this.meta.has("proxy_socks_module"))return i(Error("Socks module not loaded"));let n=n=>{let a=!!this.meta.get("proxy_socks_module").SocksClient,r=a?this.meta.get("proxy_socks_module").SocksClient:this.meta.get("proxy_socks_module"),o=Number(t.protocol.replace(/\D/g,""))||5,s={proxy:{ipaddress:n,port:Number(t.port),type:o},[a?"destination":"target"]:{host:e.host,port:e.port},command:"connect"};if(t.auth){let e=decodeURIComponent(t.auth.split(":").shift()),i=decodeURIComponent(t.auth.split(":").pop());a?(s.proxy.userId=e,s.proxy.password=i):4===o?s.userid=e:s.authentication={username:e,password:i}}r.createConnection(s,(e,t)=>e?i(e):i(null,{connection:t.socket||t}))};if(m.isIP(t.hostname))return n(t.hostname);return h.resolve(t.hostname,(e,t)=>{if(e)return i(e);n(Array.isArray(t)?t[0]:t)})}}i(Error("Unknown proxy configuration"))}}_convertDataImages(e,t){if(!this.options.attachDataUrls&&!e.data.attachDataUrls||!e.data.html)return t();e.resolveContent(e.data,"html",(i,n)=>{if(i)return t(i);let a=0;n=(n||"").toString().replace(/(<img\b[^<>]{0,1024} src\s{0,20}=[\s"']{0,20})(data:([^;]+);[^"'>\s]+)/gi,(t,i,n,o)=>{let s=f.randomBytes(10).toString("hex")+"@localhost";return e.data.attachments||(e.data.attachments=[]),Array.isArray(e.data.attachments)||(e.data.attachments=[].concat(e.data.attachments||[])),e.data.attachments.push({path:n,cid:s,filename:"image-"+ ++a+"."+r.detectExtension(o)}),i+"cid:"+s}),e.data.html=n,t()})}set(e,t){return this.meta.set(e,t)}get(e){return this.meta.get(e)}}e.exports=g},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22226:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.default,__next_app__:()=>p,pages:()=>c,routeModule:()=>d,tree:()=>l});var n=i(65239),a=i(48088),r=i(31369),o=i(30893),s={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);i.d(t,s);let l={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,43839)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(i.bind(i,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(i.bind(i,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\contact\\page.tsx"],p={require:i,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},23302:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"InvariantError",{enumerable:!0,get:function(){return i}});class i extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},23879:(e,t)=>{"use strict";function i(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return i}})},25008:(e,t,i)=>{"use strict";let n=i(91645),a=i(34631),r=i(79551);e.exports=function e(t,i,o,s){let l,c,p,d=r.parse(t);l={host:d.hostname,port:Number(d.port)?Number(d.port):"https:"===d.protocol?443:80},"https:"===d.protocol?(l.rejectUnauthorized=!1,c=a.connect.bind(a)):c=n.connect.bind(n);let u=!1,m=e=>{if(!u){u=!0;try{p.destroy()}catch(e){}s(e)}},h=()=>{let e=Error("Proxy socket timed out");e.code="ETIMEDOUT",m(e)};(p=c(l,()=>{if(u)return;let e={Host:o+":"+i,Connection:"close"};d.auth&&(e["Proxy-Authorization"]="Basic "+Buffer.from(d.auth).toString("base64")),p.write("CONNECT "+o+":"+i+" HTTP/1.1\r\n"+Object.keys(e).map(t=>t+": "+e[t]).join("\r\n")+"\r\n\r\n");let t="",n=e=>{let i,a;if(!u&&(t+=e.toString("binary"),i=t.match(/\r\n\r\n/))){if(p.removeListener("data",n),a=t.substr(i.index+i[0].length),t=t.substr(0,i.index),a&&p.unshift(Buffer.from(a,"binary")),u=!0,!(i=t.match(/^HTTP\/\d+\.\d+ (\d+)/i))||"2"!==(i[1]||"").charAt(0)){try{p.destroy()}catch(e){}return s(Error("Invalid response from proxy"+(i&&": "+i[1]||"")))}return p.removeListener("error",m),p.removeListener("timeout",h),p.setTimeout(0),s(null,p)}};p.on("data",n)})).setTimeout(e.timeout||3e4),p.on("timeout",h),p.once("error",m)}},26434:(e,t,i)=>{"use strict";let n=i(41957),a=i(34e3),r=i(55511);function o(e,t,i){let n=new Set,a=new Set,r=new Map;(i||"").toLowerCase().split(":").forEach(e=>{a.add(e.trim())}),(t||"").toLowerCase().split(":").filter(e=>!a.has(e.trim())).forEach(e=>{n.add(e.trim())});for(let t=e.length-1;t>=0;t--){let i=e[t];n.has(i.key)&&!r.has(i.key)&&r.set(i.key,s(i.line))}let o=[],l=[];return n.forEach(e=>{r.has(e)&&(l.push(e),o.push(e+":"+r.get(e)))}),{headers:o.join("\r\n")+"\r\n",fieldNames:l.join(":")}}function s(e){return e.substr(e.indexOf(":")+1).replace(/\r?\n/g,"").replace(/\s+/g," ").trim()}e.exports=(e,t,i,l)=>{let c,p,d=o(e,(l=l||{}).headerFieldNames||"From:Sender:Reply-To:Subject:Date:Message-ID:To:Cc:MIME-Version:Content-Type:Content-Transfer-Encoding:Content-ID:Content-Description:Resent-Date:Resent-From:Resent-Sender:Resent-To:Resent-Cc:Resent-Message-ID:In-Reply-To:References:List-Id:List-Help:List-Unsubscribe:List-Subscribe:List-Post:List-Owner:List-Archive",l.skipFields),u=function(e,t,i,r,o){let s=["v=1","a=rsa-"+r,"c=relaxed/relaxed","d="+n.toASCII(e),"q=dns/txt","s="+t,"bh="+o,"h="+i].join("; ");return a.foldLines("DKIM-Signature: "+s,76)+";\r\n b="}(l.domainName,l.keySelector,d.fieldNames,t,i);d.headers+="dkim-signature:"+s(u),(c=r.createSign(("rsa-"+t).toUpperCase())).update(d.headers);try{p=c.sign(l.privateKey,"base64")}catch(e){return!1}return u+p.replace(/(^.{73}|.{75}(?!\r?\n|\r))/g,"$&\r\n ").trim()},e.exports.relaxedHeaders=o},27207:(e,t,i)=>{"use strict";let n=i(94735),a=i(93384),r=i(12501),o=i(58812),s=i(89581),l=i(91423);class c extends n{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=s.parseConnectionUrl(e.url),i=i||t.service),this.options=s.assign(!1,e,t,i&&o(i)),this.options.maxConnections=this.options.maxConnections||5,this.options.maxMessages=this.options.maxMessages||100,this.logger=s.getLogger(this.options,{component:this.options.component||"smtp-pool"});let n=new r(this.options);this.name="SMTP (pool)",this.version=l.version+"[client:"+n.version+"]",this._rateLimit={counter:0,timeout:null,waiting:[],checkpoint:!1,delta:Number(this.options.rateDelta)||1e3,limit:Number(this.options.rateLimit)||0},this._closed=!1,this._queue=[],this._connections=[],this._connectionCounter=0,this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}getSocket(e,t){return setImmediate(()=>t(null,!1))}send(e,t){return!this._closed&&(this._queue.push({mail:e,requeueAttempts:0,callback:t}),this.idling&&this._queue.length>=this.options.maxConnections&&(this.idling=!1),setImmediate(()=>this._processMessages()),!0)}close(){let e,t=this._connections.length;if(this._closed=!0,clearTimeout(this._rateLimit.timeout),!t&&!this._queue.length)return;for(let i=t-1;i>=0;i--)this._connections[i]&&this._connections[i].available&&((e=this._connections[i]).close(),this.logger.info({tnx:"connection",cid:e.id,action:"removed"},"Connection #%s removed",e.id));if(t&&!this._connections.length&&this.logger.debug({tnx:"connection"},"All connections removed"),!this._queue.length)return;let i=()=>{if(!this._queue.length)return void this.logger.debug({tnx:"connection"},"Pending queue entries cleared");let t=this._queue.shift();if(t&&"function"==typeof t.callback)try{t.callback(Error("Connection pool was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}setImmediate(i)};setImmediate(i)}_processMessages(){let e,t,i;if(this._closed)return;if(!this._queue.length){this.idling||(this.idling=!0,this.emit("idle"));return}for(t=0,i=this._connections.length;t<i;t++)if(this._connections[t].available){e=this._connections[t];break}if(!e&&this._connections.length<this.options.maxConnections&&(e=this._createConnection()),!e){this.idling=!1;return}!this.idling&&this._queue.length<this.options.maxConnections&&(this.idling=!0,this.emit("idle"));let n=e.queueEntry=this._queue.shift();n.messageId=(e.queueEntry.mail.message.getHeader("message-id")||"").replace(/[<>\s]/g,""),e.available=!1,this.logger.debug({tnx:"pool",cid:e.id,messageId:n.messageId,action:"assign"},"Assigned message <%s> to #%s (%s)",n.messageId,e.id,e.messages+1),this._rateLimit.limit&&(this._rateLimit.counter++,this._rateLimit.checkpoint||(this._rateLimit.checkpoint=Date.now())),e.send(n.mail,(t,i)=>{if(n===e.queueEntry){try{n.callback(t,i)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}})}_createConnection(){let e=new a(this);return e.id=++this._connectionCounter,this.logger.info({tnx:"pool",cid:e.id,action:"conection"},"Created new pool resource #%s",e.id),e.on("available",()=>{this.logger.debug({tnx:"connection",cid:e.id,action:"available"},"Connection #%s became available",e.id),this._closed?this.close():this._processMessages()}),e.once("error",t=>{if("EMAXLIMIT"!==t.code?this.logger.error({err:t,tnx:"pool",cid:e.id},"Pool Error for #%s: %s",e.id,t.message):this.logger.debug({tnx:"pool",cid:e.id,action:"maxlimit"},"Max messages limit exchausted for #%s",e.id),e.queueEntry){try{e.queueEntry.callback(t)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}this._removeConnection(e),this._continueProcessing()}),e.once("close",()=>{this.logger.info({tnx:"connection",cid:e.id,action:"closed"},"Connection #%s was closed",e.id),this._removeConnection(e),e.queueEntry?setTimeout(()=>{e.queueEntry&&(this._shouldRequeuOnConnectionClose(e.queueEntry)?this._requeueEntryOnConnectionClose(e):this._failDeliveryOnConnectionClose(e)),this._continueProcessing()},50):this._continueProcessing()}),this._connections.push(e),e}_shouldRequeuOnConnectionClose(e){return void 0===this.options.maxRequeues||this.options.maxRequeues<0||e.requeueAttempts<this.options.maxRequeues}_failDeliveryOnConnectionClose(e){if(e.queueEntry&&e.queueEntry.callback){try{e.queueEntry.callback(Error("Reached maximum number of retries after connection was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",messageId:e.queueEntry.messageId,cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}}_requeueEntryOnConnectionClose(e){e.queueEntry.requeueAttempts=e.queueEntry.requeueAttempts+1,this.logger.debug({tnx:"pool",cid:e.id,messageId:e.queueEntry.messageId,action:"requeue"},"Re-queued message <%s> for #%s. Attempt: #%s",e.queueEntry.messageId,e.id,e.queueEntry.requeueAttempts),this._queue.unshift(e.queueEntry),e.queueEntry=!1}_continueProcessing(){this._closed?this.close():setTimeout(()=>this._processMessages(),100)}_removeConnection(e){let t=this._connections.indexOf(e);-1!==t&&this._connections.splice(t,1)}_checkRateLimit(e){if(!this._rateLimit.limit)return e();let t=Date.now();return this._rateLimit.counter<this._rateLimit.limit?e():(this._rateLimit.waiting.push(e),this._rateLimit.checkpoint<=t-this._rateLimit.delta)?this._clearRateLimit():void(!this._rateLimit.timeout&&(this._rateLimit.timeout=setTimeout(()=>this._clearRateLimit(),this._rateLimit.delta-(t-this._rateLimit.checkpoint)),this._rateLimit.checkpoint=t))}_clearRateLimit(){for(clearTimeout(this._rateLimit.timeout),this._rateLimit.timeout=null,this._rateLimit.counter=0,this._rateLimit.checkpoint=!1;this._rateLimit.waiting.length;)setImmediate(this._rateLimit.waiting.shift())}isIdle(){return this.idling}verify(e){let t;e||(t=new Promise((t,i)=>{e=s.callbackPromise(t,i)}));let i=new a(this).auth;return this.getSocket(this.options,(t,n)=>{if(t)return e(t);let a=this.options;n&&n.connection&&(this.logger.info({tnx:"proxy",remoteAddress:n.connection.remoteAddress,remotePort:n.connection.remotePort,destHost:a.host||"",destPort:a.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",n.connection.remoteAddress,n.connection.remotePort,a.host||"",a.port||""),a=s.assign(!1,a),Object.keys(n).forEach(e=>{a[e]=n[e]}));let o=new r(a),l=!1;o.once("error",t=>{if(!l)return l=!0,o.close(),e(t)}),o.once("end",()=>{if(!l)return l=!0,e(Error("Connection closed"))});let c=()=>{if(!l)return l=!0,o.quit(),e(null,!0)};o.connect(()=>{if(!l)if(i&&(o.allowsAuth||a.forceAuth))o.login(i,t=>{if(!l){if(t)return l=!0,o.close(),e(t);c()}});else if(!i&&o.allowsAuth&&a.forceAuth){let t=Error("Authentication info was not provided");return t.code="NoAuth",l=!0,o.close(),e(t)}else c()})}),t}}e.exports=c},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29564:(e,t,i)=>{"use strict";let n=i(81630),a=i(55591),r=i(79551),o=i(74075),s=i(27910).PassThrough,l=i(75113),c=i(91423),p=i(91645);e.exports=function(e,t){return function e(t,i){let d,u,m;(i=i||{}).fetchRes=i.fetchRes||new s,i.cookies=i.cookies||new l,i.redirects=i.redirects||0,i.maxRedirects=isNaN(i.maxRedirects)?5:i.maxRedirects,i.cookie&&([].concat(i.cookie||[]).forEach(e=>{i.cookies.set(e,t)}),i.cookie=!1);let h=i.fetchRes,f=r.parse(t),g=(i.method||"").toString().trim().toUpperCase()||"GET",v=!1,x="https:"===f.protocol?a:n,b={"accept-encoding":"gzip,deflate","user-agent":"nodemailer/"+c.version};if(Object.keys(i.headers||{}).forEach(e=>{b[e.toLowerCase().trim()]=i.headers[e]}),i.userAgent&&(b["user-agent"]=i.userAgent),f.auth&&(b.Authorization="Basic "+Buffer.from(f.auth).toString("base64")),(d=i.cookies.get(t))&&(b.cookie=d),i.body){if(!1!==i.contentType&&(b["Content-Type"]=i.contentType||"application/x-www-form-urlencoded"),"function"==typeof i.body.pipe)b["Transfer-Encoding"]="chunked",(u=i.body).on("error",e=>{v||(v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e))});else{if(i.body instanceof Buffer)u=i.body;else if("object"==typeof i.body)try{u=Buffer.from(Object.keys(i.body).map(e=>{let t=i.body[e].toString().trim();return encodeURIComponent(e)+"="+encodeURIComponent(t)}).join("&"))}catch(e){if(v)return;v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e);return}else u=Buffer.from(i.body.toString().trim());b["Content-Type"]=i.contentType||"application/x-www-form-urlencoded",b["Content-Length"]=u.length}g=(i.method||"").toString().trim().toUpperCase()||"POST"}let y={method:g,host:f.hostname,path:f.path,port:f.port?f.port:"https:"===f.protocol?443:80,headers:b,rejectUnauthorized:!1,agent:!1};i.tls&&Object.keys(i.tls).forEach(e=>{y[e]=i.tls[e]}),"https:"!==f.protocol||!f.hostname||f.hostname===y.host||p.isIP(f.hostname)||y.servername||(y.servername=f.hostname);try{m=x.request(y)}catch(e){return v=!0,setImmediate(()=>{e.type="FETCH",e.sourceUrl=t,h.emit("error",e)}),h}return i.timeout&&m.setTimeout(i.timeout,()=>{if(v)return;v=!0,m.abort();let e=Error("Request Timeout");e.type="FETCH",e.sourceUrl=t,h.emit("error",e)}),m.on("error",e=>{v||(v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e))}),m.on("response",n=>{let a;if(!v){switch(n.headers["content-encoding"]){case"gzip":case"deflate":a=o.createUnzip()}if(n.headers["set-cookie"]&&[].concat(n.headers["set-cookie"]||[]).forEach(e=>{i.cookies.set(e,t)}),[301,302,303,307,308].includes(n.statusCode)&&n.headers.location){if(i.redirects++,i.redirects>i.maxRedirects){v=!0;let e=Error("Maximum redirect count exceeded");e.type="FETCH",e.sourceUrl=t,h.emit("error",e),m.abort();return}return i.method="GET",i.body=!1,e(r.resolve(t,n.headers.location),i)}if(h.statusCode=n.statusCode,h.headers=n.headers,n.statusCode>=300&&!i.allowErrorResponse){v=!0;let e=Error("Invalid status code "+n.statusCode);e.type="FETCH",e.sourceUrl=t,h.emit("error",e),m.abort();return}n.on("error",e=>{v||(v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e),m.abort())}),a?(n.pipe(a).pipe(h),a.on("error",e=>{v||(v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e),m.abort())})):n.pipe(h)}}),setImmediate(()=>{if(u)try{if("function"==typeof u.pipe)return u.pipe(m);m.write(u)}catch(e){v=!0,e.type="FETCH",e.sourceUrl=t,h.emit("error",e);return}m.end()}),h}(e,t)},e.exports.Cookies=l},31237:(e,t,i)=>{"use strict";let n=i(27910).Transform;class a extends n{constructor(e){super(e),this.options=e||{}}_transform(e,t,i){let n,a=0;for(let t=0,i=e.length;t<i;t++)13===e[t]&&(n=e.slice(a,t),a=t+1,this.push(n));a&&a<e.length?(n=e.slice(a),this.push(n)):a||this.push(e),i()}}e.exports=a},33331:(e,t)=>{"use strict";function i(e){for(let t=0;t<e.length;t++){let i=e[t];if("function"!=typeof i)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof i}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return i}})},33873:e=>{"use strict";e.exports=require("path")},34e3:(e,t,i)=>{"use strict";let n=i(34295),a=i(59021),r=i(77400);e.exports={isPlainText:(e,t)=>!("string"!=typeof e||(t?/[\x00-\x08\x0b\x0c\x0e-\x1f"\u0080-\uFFFF]/:/[\x00-\x08\x0b\x0c\x0e-\x1f\u0080-\uFFFF]/).test(e)),hasLongerLines:(e,t)=>e.length>131072||RegExp("^.{"+(t+1)+",}","m").test(e),encodeWord(e,t,i){let r;t=(t||"Q").toString().toUpperCase().trim().charAt(0);let o="UTF-8";if((i=i||0)&&i>7+o.length&&(i-=7+o.length),"Q"===t?r=a.encode(e).replace(/[^a-z0-9!*+\-/=]/gi,e=>{let t=e.charCodeAt(0).toString(16).toUpperCase();return" "===e?"_":"="+(1===t.length?"0"+t:t)}):"B"===t&&(r="string"==typeof e?e:n.encode(e),i=i?Math.max(3,(i-i%4)/4*3):0),i&&("B"!==t?r:n.encode(e)).length>i)if("Q"===t)r=this.splitMimeEncodedString(r,i).join("?= =?"+o+"?"+t+"?");else{let e=[],a="";for(let t=0,o=r.length;t<o;t++){let s=r.charAt(t);/[\ud83c\ud83d\ud83e]/.test(s)&&t<o-1&&(s+=r.charAt(++t)),Buffer.byteLength(a+s)<=i||0===t?a+=s:(e.push(n.encode(a)),a=s)}a&&e.push(n.encode(a)),r=e.length>1?e.join("?= =?"+o+"?"+t+"?"):e.join("")}else"B"===t&&(r=n.encode(e));return"=?"+o+"?"+t+"?"+r+("?="===r.substr(-2)?"":"?=")},encodeWords(e,t,i,n){i=i||0;let a=e.match(/(?:^|\s)([^\s]*["\u0080-\uFFFF])/);if(!a)return e;if(n)return this.encodeWord(e,t,i);let r=e.match(/(["\u0080-\uFFFF][^\s]*)[^"\u0080-\uFFFF]*$/);if(!r)return e;let o=a.index+(a[0].match(/[^\s]/)||{index:0}).index,s=r.index+(r[1]||"").length;return(o?e.substr(0,o):"")+this.encodeWord(e.substring(o,s),t||"Q",i)+(s<e.length?e.substr(s):"")},buildHeaderValue(e){let t=[];return Object.keys(e.params||{}).forEach(i=>{let n=e.params[i];!this.isPlainText(n,!0)||n.length>=75?this.buildHeaderParam(i,n,50).forEach(e=>{/[\s"\\;:/=(),<>@[\]?]|^[-']|'$/.test(e.value)&&"*"!==e.key.substr(-1)?t.push(e.key+"="+JSON.stringify(e.value)):t.push(e.key+"="+e.value)}):/[\s'"\\;:/=(),<>@[\]?]|^-/.test(n)?t.push(i+"="+JSON.stringify(n)):t.push(i+"="+n)}),e.value+(t.length?"; "+t.join("; "):"")},buildHeaderParam(e,t,i){let n,a,r,o,s,l,c=[],p="string"==typeof t?t:(t||"").toString(),d=0;if(i=i||50,this.isPlainText(t,!0)){if(p.length<=i)return[{key:e,value:p}];(p=p.replace(RegExp(".{"+i+"}","g"),e=>(c.push({line:e}),"")))&&c.push({line:p})}else{if(/[\uD800-\uDBFF]/.test(p)){for(s=0,n=[],l=p.length;s<l;s++)(r=(a=p.charAt(s)).charCodeAt(0))>=55296&&r<=56319&&s<l-1?(a+=p.charAt(s+1),n.push(a),s++):n.push(a);p=n}o="utf-8''";let e=!0;for(s=0,d=0,l=p.length;s<l;s++){if(a=p[s],e)a=this.safeEncodeURIComponent(a);else if((a=" "===a?a:this.safeEncodeURIComponent(a))!==p[s])if((this.safeEncodeURIComponent(o)+a).length>=i)c.push({line:o,encoded:e}),o="",d=s-1;else{e=!0,s=d,o="";continue}(o+a).length>=i?(c.push({line:o,encoded:e}),o=a=" "===p[s]?" ":this.safeEncodeURIComponent(p[s]),a===p[s]?(e=!1,d=s-1):e=!0):o+=a}o&&c.push({line:o,encoded:e})}return c.map((t,i)=>({key:e+"*"+i+(t.encoded?"*":""),value:t.line}))},parseHeaderValue(e){let t,i={value:!1,params:{}},n=!1,a="",r="value",o=!1,s=!1;for(let l=0,c=e.length;l<c;l++)if(t=e.charAt(l),"key"===r){if("="===t){n=a.trim().toLowerCase(),r="value",a="";continue}a+=t}else{if(s)a+=t;else if("\\"===t){s=!0;continue}else o&&t===o?o=!1:o||'"'!==t?o||";"!==t?a+=t:(!1===n?i.value=a.trim():i.params[n]=a.trim(),r="key",a=""):o=t;s=!1}return"value"===r?!1===n?i.value=a.trim():i.params[n]=a.trim():a.trim()&&(i.params[a.trim().toLowerCase()]=""),Object.keys(i.params).forEach(e=>{let t,n,a,r;(a=e.match(/(\*(\d+)|\*(\d+)\*|\*)$/))&&(t=e.substr(0,a.index),n=Number(a[2]||a[3])||0,i.params[t]&&"object"==typeof i.params[t]||(i.params[t]={charset:!1,values:[]}),r=i.params[e],0===n&&"*"===a[0].substr(-1)&&(a=r.match(/^([^']*)'[^']*'(.*)$/))&&(i.params[t].charset=a[1]||"iso-8859-1",r=a[2]),i.params[t].values[n]=r,delete i.params[e])}),Object.keys(i.params).forEach(e=>{let t;i.params[e]&&Array.isArray(i.params[e].values)&&(t=i.params[e].values.map(e=>e||"").join(""),i.params[e].charset?i.params[e]="=?"+i.params[e].charset+"?Q?"+t.replace(/[=?_\s]/g,e=>{let t=e.charCodeAt(0).toString(16);return" "===e?"_":"%"+(t.length<2?"0":"")+t}).replace(/%/g,"=")+"?=":i.params[e]=t)}),i},detectExtension:e=>r.detectExtension(e),detectMimeType:e=>r.detectMimeType(e),foldLines(e,t,i){e=(e||"").toString(),t=t||76;let n=0,a=e.length,r="",o,s;for(;n<a;){if((o=e.substr(n,t)).length<t){r+=o;break}if(s=o.match(/^[^\n\r]*(\r?\n|\r)/)){r+=o=s[0],n+=o.length;continue}(s=o.match(/(\s+)[^\s]*$/))&&s[0].length-(i?(s[1]||"").length:0)<o.length?o=o.substr(0,o.length-(s[0].length-(i?(s[1]||"").length:0))):(s=e.substr(n+o.length).match(/^[^\s]+(\s*)/))&&(o+=s[0].substr(0,s[0].length-(i?0:(s[1]||"").length))),r+=o,(n+=o.length)<a&&(r+="\r\n")}return r},splitMimeEncodedString:(e,t)=>{let i,n,a,r,o=[];for(t=Math.max(t||0,12);e.length;){for((n=(i=e.substr(0,t)).match(/[=][0-9A-F]?$/i))&&(i=i.substr(0,n.index)),r=!1;!r;)r=!0,(n=e.substr(i.length).match(/^[=]([0-9A-F]{2})/i))&&(a=parseInt(n[1],16))<194&&a>127&&(i=i.substr(0,i.length-3),r=!1);i.length&&o.push(i),e=e.substr(i.length)}return o},encodeURICharComponent:e=>{let t="",i=e.charCodeAt(0).toString(16).toUpperCase();if(i.length%2&&(i="0"+i),i.length>2)for(let e=0,n=i.length/2;e<n;e++)t+="%"+i.substr(e,2);else t+="%"+i;return t},safeEncodeURIComponent(e){e=(e||"").toString();try{e=encodeURIComponent(e)}catch(t){return e.replace(/[^\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]+/g,"")}return e.replace(/[\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]/g,e=>this.encodeURICharComponent(e))}}},34295:(e,t,i)=>{"use strict";let n=i(27910).Transform;function a(e){return"string"==typeof e&&(e=Buffer.from(e,"utf-8")),e.toString("base64")}function r(e,t){if(e=(e||"").toString(),t=t||76,e.length<=t)return e;let i=[],n=0,a=1024*t;for(;n<e.length;){let r=e.substr(n,a).replace(RegExp(".{"+t+"}","g"),"$&\r\n").trim();i.push(r),n+=a}return i.join("\r\n").trim()}class o extends n{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this._remainingBytes=!1,this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return setImmediate(i);this.inputBytes+=e.length,this._remainingBytes&&this._remainingBytes.length&&(e=Buffer.concat([this._remainingBytes,e],this._remainingBytes.length+e.length),this._remainingBytes=!1),e.length%3?(this._remainingBytes=e.slice(e.length-e.length%3),e=e.slice(0,e.length-e.length%3)):this._remainingBytes=!1;let n=this._curLine+a(e);if(this.options.lineLength){let e=(n=r(n,this.options.lineLength)).lastIndexOf("\n");e<0?(this._curLine=n,n=""):e===n.length-1?this._curLine="":(this._curLine=n.substr(e+1),n=n.substr(0,e+1))}n&&(this.outputBytes+=n.length,this.push(Buffer.from(n,"ascii"))),setImmediate(i)}_flush(e){this._remainingBytes&&this._remainingBytes.length&&(this._curLine+=a(this._remainingBytes)),this._curLine&&(this._curLine=r(this._curLine,this.options.lineLength),this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii"),this._curLine=""),e()}}e.exports={encode:a,wrap:r,Encoder:o}},34360:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{chainStreams:function(){return u},continueDynamicHTMLResume:function(){return A},continueDynamicPrerender:function(){return S},continueFizzStream:function(){return k},continueStaticPrerender:function(){return T},createBufferedTransformStream:function(){return v},createDocumentClosingStream:function(){return O},createRootLayoutValidatorStream:function(){return E},renderToInitialFizzStream:function(){return x},streamFromBuffer:function(){return h},streamFromString:function(){return m},streamToBuffer:function(){return f},streamToString:function(){return g}});let n=i(44334),a=i(2720),r=i(13944),o=i(97748),s=i(42503),l=i(88477),c=i(54806);function p(){}let d=new TextEncoder;function u(...e){if(0===e.length)throw Object.defineProperty(Error("Invariant: chainStreams requires at least one stream"),"__NEXT_ERROR_CODE",{value:"E437",enumerable:!1,configurable:!0});if(1===e.length)return e[0];let{readable:t,writable:i}=new TransformStream,n=e[0].pipeTo(i,{preventClose:!0}),a=1;for(;a<e.length-1;a++){let t=e[a];n=n.then(()=>t.pipeTo(i,{preventClose:!0}))}let r=e[a];return(n=n.then(()=>r.pipeTo(i))).catch(p),t}function m(e){return new ReadableStream({start(t){t.enqueue(d.encode(e)),t.close()}})}function h(e){return new ReadableStream({start(t){t.enqueue(e),t.close()}})}async function f(e){let t=e.getReader(),i=[];for(;;){let{done:e,value:n}=await t.read();if(e)break;i.push(n)}return Buffer.concat(i)}async function g(e,t){let i=new TextDecoder("utf-8",{fatal:!0}),n="";for await(let a of e){if(null==t?void 0:t.aborted)return n;n+=i.decode(a,{stream:!0})}return n+i.decode()}function v(){let e,t=[],i=0,n=n=>{if(e)return;let a=new r.DetachedPromise;e=a,(0,o.scheduleImmediate)(()=>{try{let e=new Uint8Array(i),a=0;for(let i=0;i<t.length;i++){let n=t[i];e.set(n,a),a+=n.byteLength}t.length=0,i=0,n.enqueue(e)}catch{}finally{e=void 0,a.resolve()}})};return new TransformStream({transform(e,a){t.push(e),i+=e.byteLength,n(a)},flush(){if(e)return e.promise}})}function x({ReactDOMServer:e,element:t,streamOptions:i}){return(0,n.getTracer)().trace(a.AppRenderSpan.renderToReadableStream,async()=>e.renderToReadableStream(t,i))}function b(e){let t=!1,i=!1;return new TransformStream({async transform(n,a){i=!0;let r=await e();if(t){if(r){let e=d.encode(r);a.enqueue(e)}a.enqueue(n)}else{let e=(0,l.indexOfUint8Array)(n,s.ENCODED_TAGS.CLOSED.HEAD);if(-1!==e){if(r){let t=d.encode(r),i=new Uint8Array(n.length+t.length);i.set(n.slice(0,e)),i.set(t,e),i.set(n.slice(e),e+t.length),a.enqueue(i)}else a.enqueue(n);t=!0}else r&&a.enqueue(d.encode(r)),a.enqueue(n),t=!0}},async flush(t){if(i){let i=await e();i&&t.enqueue(d.encode(i))}}})}function y(e){let t=null,i=!1;async function n(n){if(t)return;let a=e.getReader();await (0,o.atLeastOneTask)();try{for(;;){let{done:e,value:t}=await a.read();if(e){i=!0;return}n.enqueue(t)}}catch(e){n.error(e)}}return new TransformStream({transform(e,i){i.enqueue(e),t||(t=n(i))},flush(e){if(!i)return t||n(e)}})}let _="</body></html>";function w(){let e=!1;return new TransformStream({transform(t,i){if(e)return i.enqueue(t);let n=(0,l.indexOfUint8Array)(t,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML);if(n>-1){if(e=!0,t.length===s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length)return;let a=t.slice(0,n);if(i.enqueue(a),t.length>s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length+n){let e=t.slice(n+s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length);i.enqueue(e)}}else i.enqueue(t)},flush(e){e.enqueue(s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)}})}function E(){let e=!1,t=!1;return new TransformStream({async transform(i,n){!e&&(0,l.indexOfUint8Array)(i,s.ENCODED_TAGS.OPENING.HTML)>-1&&(e=!0),!t&&(0,l.indexOfUint8Array)(i,s.ENCODED_TAGS.OPENING.BODY)>-1&&(t=!0),n.enqueue(i)},flush(i){let n=[];e||n.push("html"),t||n.push("body"),n.length&&i.enqueue(d.encode(`<html id="__next_error__">
            <template
              data-next-error-message="Missing ${n.map(e=>`<${e}>`).join(n.length>1?" and ":"")} tags in the root layout.
Read more at https://nextjs.org/docs/messages/missing-root-layout-tags""
              data-next-error-digest="${c.MISSING_ROOT_TAGS_ERROR}"
              data-next-error-stack=""
            ></template>
          `))}})}async function k(e,{suffix:t,inlinedDataStream:i,isStaticGeneration:n,getServerInsertedHTML:a,getServerInsertedMetadata:s,validateRootLayout:l}){let c=t?t.split(_,1)[0]:null;n&&"allReady"in e&&await e.allReady;var p=[v(),b(s),null!=c&&c.length>0?function(e){let t,i=!1,n=i=>{let n=new r.DetachedPromise;t=n,(0,o.scheduleImmediate)(()=>{try{i.enqueue(d.encode(e))}catch{}finally{t=void 0,n.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),i||(i=!0,n(t))},flush(n){if(t)return t.promise;i||n.enqueue(d.encode(e))}})}(c):null,i?y(i):null,l?E():null,w(),b(a)];let u=e;for(let e of p)e&&(u=u.pipeThrough(e));return u}async function S(e,{getServerInsertedHTML:t,getServerInsertedMetadata:i}){return e.pipeThrough(v()).pipeThrough(new TransformStream({transform(e,t){(0,l.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,l.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY)||(0,l.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.HTML)||(e=(0,l.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.BODY),e=(0,l.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.HTML),t.enqueue(e))}})).pipeThrough(b(t)).pipeThrough(b(i))}async function T(e,{inlinedDataStream:t,getServerInsertedHTML:i,getServerInsertedMetadata:n}){return e.pipeThrough(v()).pipeThrough(b(i)).pipeThrough(b(n)).pipeThrough(y(t)).pipeThrough(w())}async function A(e,{inlinedDataStream:t,getServerInsertedHTML:i,getServerInsertedMetadata:n}){return e.pipeThrough(v()).pipeThrough(b(i)).pipeThrough(b(n)).pipeThrough(y(t)).pipeThrough(w())}function O(){return m(_)}},34631:e=>{"use strict";e.exports=require("tls")},34821:(e,t,i)=>{"use strict";var n=i(68790),a={stream:!0},r=new Map;function o(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function s(){}function l(e){for(var t=e[1],n=[],a=0;a<t.length;){var l=t[a++];t[a++];var c=r.get(l);if(void 0===c){c=i.e(l),n.push(c);var p=r.set.bind(r,l,null);c.then(p,s),r.set(l,c)}else null!==c&&n.push(c)}return 4===e.length?0===n.length?o(e[0]):Promise.all(n).then(function(){return o(e[0])}):0<n.length?Promise.all(n):null}function c(e){var t=globalThis.__next_require__(e[0]);if(4===e.length&&"function"==typeof t.then)if("fulfilled"===t.status)t=t.value;else throw t.reason;return"*"===e[2]?t:""===e[2]?t.__esModule?t.default:t:t[e[2]]}var p=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,d=Symbol.for("react.transitional.element"),u=Symbol.for("react.lazy"),m=Symbol.iterator,h=Symbol.asyncIterator,f=Array.isArray,g=Object.getPrototypeOf,v=Object.prototype,x=new WeakMap;function b(e,t,i,n,a){function r(e,i){i=new Blob([new Uint8Array(i.buffer,i.byteOffset,i.byteLength)]);var n=l++;return null===p&&(p=new FormData),p.append(t+n,i),"$"+e+n.toString(16)}function o(e,_){if(null===_)return null;if("object"==typeof _){switch(_.$$typeof){case d:if(void 0!==i&&-1===e.indexOf(":")){var w,E,k,S,T,A=b.get(this);if(void 0!==A)return i.set(A+":"+e,_),"$T"}throw Error("React Element cannot be passed to Server Functions from the Client without a temporary reference set. Pass a TemporaryReferenceSet to the options.");case u:A=_._payload;var O=_._init;null===p&&(p=new FormData),c++;try{var C=O(A),N=l++,j=s(C,N);return p.append(t+N,j),"$"+N.toString(16)}catch(e){if("object"==typeof e&&null!==e&&"function"==typeof e.then){c++;var R=l++;return A=function(){try{var e=s(_,R),i=p;i.append(t+R,e),c--,0===c&&n(i)}catch(e){a(e)}},e.then(A,A),"$"+R.toString(16)}return a(e),null}finally{c--}}if("function"==typeof _.then){null===p&&(p=new FormData),c++;var P=l++;return _.then(function(e){try{var i=s(e,P);(e=p).append(t+P,i),c--,0===c&&n(e)}catch(e){a(e)}},a),"$@"+P.toString(16)}if(void 0!==(A=b.get(_)))if(y!==_)return A;else y=null;else -1===e.indexOf(":")&&void 0!==(A=b.get(this))&&(e=A+":"+e,b.set(_,e),void 0!==i&&i.set(e,_));if(f(_))return _;if(_ instanceof FormData){null===p&&(p=new FormData);var M=p,I=t+(e=l++)+"_";return _.forEach(function(e,t){M.append(I+t,e)}),"$K"+e.toString(16)}if(_ instanceof Map)return e=l++,A=s(Array.from(_),e),null===p&&(p=new FormData),p.append(t+e,A),"$Q"+e.toString(16);if(_ instanceof Set)return e=l++,A=s(Array.from(_),e),null===p&&(p=new FormData),p.append(t+e,A),"$W"+e.toString(16);if(_ instanceof ArrayBuffer)return e=new Blob([_]),A=l++,null===p&&(p=new FormData),p.append(t+A,e),"$A"+A.toString(16);if(_ instanceof Int8Array)return r("O",_);if(_ instanceof Uint8Array)return r("o",_);if(_ instanceof Uint8ClampedArray)return r("U",_);if(_ instanceof Int16Array)return r("S",_);if(_ instanceof Uint16Array)return r("s",_);if(_ instanceof Int32Array)return r("L",_);if(_ instanceof Uint32Array)return r("l",_);if(_ instanceof Float32Array)return r("G",_);if(_ instanceof Float64Array)return r("g",_);if(_ instanceof BigInt64Array)return r("M",_);if(_ instanceof BigUint64Array)return r("m",_);if(_ instanceof DataView)return r("V",_);if("function"==typeof Blob&&_ instanceof Blob)return null===p&&(p=new FormData),e=l++,p.append(t+e,_),"$B"+e.toString(16);if(e=null===(w=_)||"object"!=typeof w?null:"function"==typeof(w=m&&w[m]||w["@@iterator"])?w:null)return(A=e.call(_))===_?(e=l++,A=s(Array.from(A),e),null===p&&(p=new FormData),p.append(t+e,A),"$i"+e.toString(16)):Array.from(A);if("function"==typeof ReadableStream&&_ instanceof ReadableStream)return function(e){try{var i,r,s,d,u,m,h,f=e.getReader({mode:"byob"})}catch(d){return i=e.getReader(),null===p&&(p=new FormData),r=p,c++,s=l++,i.read().then(function e(l){if(l.done)r.append(t+s,"C"),0==--c&&n(r);else try{var p=JSON.stringify(l.value,o);r.append(t+s,p),i.read().then(e,a)}catch(e){a(e)}},a),"$R"+s.toString(16)}return d=f,null===p&&(p=new FormData),u=p,c++,m=l++,h=[],d.read(new Uint8Array(1024)).then(function e(i){i.done?(i=l++,u.append(t+i,new Blob(h)),u.append(t+m,'"$o'+i.toString(16)+'"'),u.append(t+m,"C"),0==--c&&n(u)):(h.push(i.value),d.read(new Uint8Array(1024)).then(e,a))},a),"$r"+m.toString(16)}(_);if("function"==typeof(e=_[h]))return E=_,k=e.call(_),null===p&&(p=new FormData),S=p,c++,T=l++,E=E===k,k.next().then(function e(i){if(i.done){if(void 0===i.value)S.append(t+T,"C");else try{var r=JSON.stringify(i.value,o);S.append(t+T,"C"+r)}catch(e){a(e);return}0==--c&&n(S)}else try{var s=JSON.stringify(i.value,o);S.append(t+T,s),k.next().then(e,a)}catch(e){a(e)}},a),"$"+(E?"x":"X")+T.toString(16);if((e=g(_))!==v&&(null===e||null!==g(e))){if(void 0===i)throw Error("Only plain objects, and a few built-ins, can be passed to Server Functions. Classes or null prototypes are not supported.");return"$T"}return _}if("string"==typeof _)return"Z"===_[_.length-1]&&this[e]instanceof Date?"$D"+_:e="$"===_[0]?"$"+_:_;if("boolean"==typeof _)return _;if("number"==typeof _)return Number.isFinite(_)?0===_&&-1/0==1/_?"$-0":_:1/0===_?"$Infinity":-1/0===_?"$-Infinity":"$NaN";if(void 0===_)return"$undefined";if("function"==typeof _){if(void 0!==(A=x.get(_)))return e=JSON.stringify({id:A.id,bound:A.bound},o),null===p&&(p=new FormData),A=l++,p.set(t+A,e),"$F"+A.toString(16);if(void 0!==i&&-1===e.indexOf(":")&&void 0!==(A=b.get(this)))return i.set(A+":"+e,_),"$T";throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.")}if("symbol"==typeof _){if(void 0!==i&&-1===e.indexOf(":")&&void 0!==(A=b.get(this)))return i.set(A+":"+e,_),"$T";throw Error("Symbols cannot be passed to a Server Function without a temporary reference set. Pass a TemporaryReferenceSet to the options.")}if("bigint"==typeof _)return"$n"+_.toString(10);throw Error("Type "+typeof _+" is not supported as an argument to a Server Function.")}function s(e,t){return"object"==typeof e&&null!==e&&(t="$"+t.toString(16),b.set(e,t),void 0!==i&&i.set(t,e)),y=e,JSON.stringify(e,o)}var l=1,c=0,p=null,b=new WeakMap,y=e,_=s(e,0);return null===p?n(_):(p.set(t+"0",_),0===c&&n(p)),function(){0<c&&(c=0,null===p?n(_):n(p))}}var y=new WeakMap;function _(e){var t=x.get(this);if(!t)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var i=null;if(null!==t.bound){if((i=y.get(t))||(n={id:t.id,bound:t.bound},o=new Promise(function(e,t){a=e,r=t}),b(n,"",void 0,function(e){if("string"==typeof e){var t=new FormData;t.append("0",e),e=t}o.status="fulfilled",o.value=e,a(e)},function(e){o.status="rejected",o.reason=e,r(e)}),i=o,y.set(t,i)),"rejected"===i.status)throw i.reason;if("fulfilled"!==i.status)throw i;t=i.value;var n,a,r,o,s=new FormData;t.forEach(function(t,i){s.append("$ACTION_"+e+":"+i,t)}),i=s,t="$ACTION_REF_"+e}else t="$ACTION_ID_"+t.id;return{name:t,method:"POST",encType:"multipart/form-data",data:i}}function w(e,t){var i=x.get(this);if(!i)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(i.id!==e)return!1;var n=i.bound;if(null===n)return 0===t;switch(n.status){case"fulfilled":return n.value.length===t;case"pending":throw n;case"rejected":throw n.reason;default:throw"string"!=typeof n.status&&(n.status="pending",n.then(function(e){n.status="fulfilled",n.value=e},function(e){n.status="rejected",n.reason=e})),n}}function E(e,t,i,n){x.has(e)||(x.set(e,{id:t,originalBind:e.bind,bound:i}),Object.defineProperties(e,{$$FORM_ACTION:{value:void 0===n?_:function(){var e=x.get(this);if(!e)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var t=e.bound;return null===t&&(t=Promise.resolve([])),n(e.id,t)}},$$IS_SIGNATURE_EQUAL:{value:w},bind:{value:T}}))}var k=Function.prototype.bind,S=Array.prototype.slice;function T(){var e=x.get(this);if(!e)return k.apply(this,arguments);var t=e.originalBind.apply(this,arguments),i=S.call(arguments,1),n=null;return n=null!==e.bound?Promise.resolve(e.bound).then(function(e){return e.concat(i)}):Promise.resolve(i),x.set(t,{id:e.id,originalBind:t.bind,bound:n}),Object.defineProperties(t,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:w},bind:{value:T}}),t}function A(e,t,i,n){this.status=e,this.value=t,this.reason=i,this._response=n}function O(e){switch(e.status){case"resolved_model":B(e);break;case"resolved_module":H(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":throw e;default:throw e.reason}}function C(e){return new A("pending",null,null,e)}function N(e,t){for(var i=0;i<e.length;i++)(0,e[i])(t)}function j(e,t,i){switch(e.status){case"fulfilled":N(t,e.value);break;case"pending":case"blocked":if(e.value)for(var n=0;n<t.length;n++)e.value.push(t[n]);else e.value=t;if(e.reason){if(i)for(t=0;t<i.length;t++)e.reason.push(i[t])}else e.reason=i;break;case"rejected":i&&N(i,e.reason)}}function R(e,t){if("pending"!==e.status&&"blocked"!==e.status)e.reason.error(t);else{var i=e.reason;e.status="rejected",e.reason=t,null!==i&&N(i,t)}}function P(e,t,i){return new A("resolved_model",(i?'{"done":true,"value":':'{"done":false,"value":')+t+"}",null,e)}function M(e,t,i){I(e,(i?'{"done":true,"value":':'{"done":false,"value":')+t+"}")}function I(e,t){if("pending"!==e.status)e.reason.enqueueModel(t);else{var i=e.value,n=e.reason;e.status="resolved_model",e.value=t,null!==i&&(B(e),j(e,i,n))}}function L(e,t){if("pending"===e.status||"blocked"===e.status){var i=e.value,n=e.reason;e.status="resolved_module",e.value=t,null!==i&&(H(e),j(e,i,n))}}A.prototype=Object.create(Promise.prototype),A.prototype.then=function(e,t){switch(this.status){case"resolved_model":B(this);break;case"resolved_module":H(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t&&t(this.reason)}};var D=null;function B(e){var t=D;D=null;var i=e.value;e.status="blocked",e.value=null,e.reason=null;try{var n=JSON.parse(i,e._response._fromJSON),a=e.value;if(null!==a&&(e.value=null,e.reason=null,N(a,n)),null!==D){if(D.errored)throw D.value;if(0<D.deps){D.value=n,D.chunk=e;return}}e.status="fulfilled",e.value=n}catch(t){e.status="rejected",e.reason=t}finally{D=t}}function H(e){try{var t=c(e.value);e.status="fulfilled",e.value=t}catch(t){e.status="rejected",e.reason=t}}function U(e,t){e._closed=!0,e._closedReason=t,e._chunks.forEach(function(e){"pending"===e.status&&R(e,t)})}function q(e){return{$$typeof:u,_payload:e,_init:O}}function z(e,t){var i=e._chunks,n=i.get(t);return n||(n=e._closed?new A("rejected",null,e._closedReason,e):C(e),i.set(t,n)),n}function F(e,t,i,n,a,r){function o(e){if(!s.errored){s.errored=!0,s.value=e;var t=s.chunk;null!==t&&"blocked"===t.status&&R(t,e)}}if(D){var s=D;s.deps++}else s=D={parent:null,chunk:null,value:null,deps:1,errored:!1};return e.then(function e(l){for(var c=1;c<r.length;c++){for(;l.$$typeof===u;)if((l=l._payload)===s.chunk)l=s.value;else if("fulfilled"===l.status)l=l.value;else{r.splice(0,c-1),l.then(e,o);return}l=l[r[c]]}c=a(n,l,t,i),t[i]=c,""===i&&null===s.value&&(s.value=c),t[0]===d&&"object"==typeof s.value&&null!==s.value&&s.value.$$typeof===d&&(l=s.value,"3"===i)&&(l.props=c),s.deps--,0===s.deps&&null!==(c=s.chunk)&&"blocked"===c.status&&(l=c.value,c.status="fulfilled",c.value=s.value,null!==l&&N(l,s.value))},o),null}function $(e,t,i,n){if(!e._serverReferenceConfig)return function(e,t,i){function n(){var e=Array.prototype.slice.call(arguments);return r?"fulfilled"===r.status?t(a,r.value.concat(e)):Promise.resolve(r).then(function(i){return t(a,i.concat(e))}):t(a,e)}var a=e.id,r=e.bound;return E(n,a,r,i),n}(t,e._callServer,e._encodeFormAction);var a=function(e,t){var i="",n=e[t];if(n)i=n.name;else{var a=t.lastIndexOf("#");if(-1!==a&&(i=t.slice(a+1),n=e[t.slice(0,a)]),!n)throw Error('Could not find the module "'+t+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.')}return n.async?[n.id,n.chunks,i,1]:[n.id,n.chunks,i]}(e._serverReferenceConfig,t.id),r=l(a);if(r)t.bound&&(r=Promise.all([r,t.bound]));else{if(!t.bound)return E(r=c(a),t.id,t.bound,e._encodeFormAction),r;r=Promise.resolve(t.bound)}if(D){var o=D;o.deps++}else o=D={parent:null,chunk:null,value:null,deps:1,errored:!1};return r.then(function(){var r=c(a);if(t.bound){var s=t.bound.value.slice(0);s.unshift(null),r=r.bind.apply(r,s)}E(r,t.id,t.bound,e._encodeFormAction),i[n]=r,""===n&&null===o.value&&(o.value=r),i[0]===d&&"object"==typeof o.value&&null!==o.value&&o.value.$$typeof===d&&(s=o.value,"3"===n)&&(s.props=r),o.deps--,0===o.deps&&null!==(r=o.chunk)&&"blocked"===r.status&&(s=r.value,r.status="fulfilled",r.value=o.value,null!==s&&N(s,o.value))},function(e){if(!o.errored){o.errored=!0,o.value=e;var t=o.chunk;null!==t&&"blocked"===t.status&&R(t,e)}}),null}function G(e,t,i,n,a){var r=parseInt((t=t.split(":"))[0],16);switch((r=z(e,r)).status){case"resolved_model":B(r);break;case"resolved_module":H(r)}switch(r.status){case"fulfilled":var o=r.value;for(r=1;r<t.length;r++){for(;o.$$typeof===u;)if("fulfilled"!==(o=o._payload).status)return F(o,i,n,e,a,t.slice(r-1));else o=o.value;o=o[t[r]]}return a(e,o,i,n);case"pending":case"blocked":return F(r,i,n,e,a,t);default:return D?(D.errored=!0,D.value=r.reason):D={parent:null,chunk:null,value:r.reason,deps:0,errored:!0},null}}function V(e,t){return new Map(t)}function X(e,t){return new Set(t)}function K(e,t){return new Blob(t.slice(1),{type:t[0]})}function W(e,t){e=new FormData;for(var i=0;i<t.length;i++)e.append(t[i][0],t[i][1]);return e}function Q(e,t){return t[Symbol.iterator]()}function J(e,t){return t}function Y(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.')}function Z(e,t,i,n,a,r,o){var s,l=new Map;this._bundlerConfig=e,this._serverReferenceConfig=t,this._moduleLoading=i,this._callServer=void 0!==n?n:Y,this._encodeFormAction=a,this._nonce=r,this._chunks=l,this._stringDecoder=new TextDecoder,this._fromJSON=null,this._rowLength=this._rowTag=this._rowID=this._rowState=0,this._buffer=[],this._closed=!1,this._closedReason=null,this._tempRefs=o,this._fromJSON=(s=this,function(e,t){if("string"==typeof t){var i=s,n=this,a=e,r=t;if("$"===r[0]){if("$"===r)return null!==D&&"0"===a&&(D={parent:D,chunk:null,value:null,deps:0,errored:!1}),d;switch(r[1]){case"$":return r.slice(1);case"L":return q(i=z(i,n=parseInt(r.slice(2),16)));case"@":if(2===r.length)return new Promise(function(){});return z(i,n=parseInt(r.slice(2),16));case"S":return Symbol.for(r.slice(2));case"F":return G(i,r=r.slice(2),n,a,$);case"T":if(n="$"+r.slice(2),null==(i=i._tempRefs))throw Error("Missing a temporary reference set but the RSC response returned a temporary reference. Pass a temporaryReference option with the set that was used with the reply.");return i.get(n);case"Q":return G(i,r=r.slice(2),n,a,V);case"W":return G(i,r=r.slice(2),n,a,X);case"B":return G(i,r=r.slice(2),n,a,K);case"K":return G(i,r=r.slice(2),n,a,W);case"Z":return er();case"i":return G(i,r=r.slice(2),n,a,Q);case"I":return 1/0;case"-":return"$-0"===r?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(r.slice(2)));case"n":return BigInt(r.slice(2));default:return G(i,r=r.slice(1),n,a,J)}}return r}if("object"==typeof t&&null!==t){if(t[0]===d){if(e={$$typeof:d,type:t[1],key:t[2],ref:null,props:t[3]},null!==D){if(D=(t=D).parent,t.errored)e=q(e=new A("rejected",null,t.value,s));else if(0<t.deps){var o=new A("blocked",null,null,s);t.value=e,t.chunk=o,e=q(o)}}}else e=t;return e}return t})}function ee(e,t,i){var n=e._chunks,a=n.get(t);a&&"pending"!==a.status?a.reason.enqueueValue(i):n.set(t,new A("fulfilled",i,null,e))}function et(e,t,i,n){var a=e._chunks,r=a.get(t);r?"pending"===r.status&&(e=r.value,r.status="fulfilled",r.value=i,r.reason=n,null!==e&&N(e,r.value)):a.set(t,new A("fulfilled",i,n,e))}function ei(e,t,i){var n=null;i=new ReadableStream({type:i,start:function(e){n=e}});var a=null;et(e,t,i,{enqueueValue:function(e){null===a?n.enqueue(e):a.then(function(){n.enqueue(e)})},enqueueModel:function(t){if(null===a){var i=new A("resolved_model",t,null,e);B(i),"fulfilled"===i.status?n.enqueue(i.value):(i.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),a=i)}else{i=a;var r=C(e);r.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),a=r,i.then(function(){a===r&&(a=null),I(r,t)})}},close:function(){if(null===a)n.close();else{var e=a;a=null,e.then(function(){return n.close()})}},error:function(e){if(null===a)n.error(e);else{var t=a;a=null,t.then(function(){return n.error(e)})}}})}function en(){return this}function ea(e,t,i){var n=[],a=!1,r=0,o={};o[h]=function(){var t,i=0;return(t={next:t=function(t){if(void 0!==t)throw Error("Values cannot be passed to next() of AsyncIterables passed to Client Components.");if(i===n.length){if(a)return new A("fulfilled",{done:!0,value:void 0},null,e);n[i]=C(e)}return n[i++]}})[h]=en,t},et(e,t,i?o[h]():o,{enqueueValue:function(t){if(r===n.length)n[r]=new A("fulfilled",{done:!1,value:t},null,e);else{var i=n[r],a=i.value,o=i.reason;i.status="fulfilled",i.value={done:!1,value:t},null!==a&&j(i,a,o)}r++},enqueueModel:function(t){r===n.length?n[r]=P(e,t,!1):M(n[r],t,!1),r++},close:function(t){for(a=!0,r===n.length?n[r]=P(e,t,!0):M(n[r],t,!0),r++;r<n.length;)M(n[r++],'"$undefined"',!0)},error:function(t){for(a=!0,r===n.length&&(n[r]=C(e));r<n.length;)R(n[r++],t)}})}function er(){var e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");return e.stack="Error: "+e.message,e}function eo(e,t){for(var i=e.length,n=t.length,a=0;a<i;a++)n+=e[a].byteLength;n=new Uint8Array(n);for(var r=a=0;r<i;r++){var o=e[r];n.set(o,a),a+=o.byteLength}return n.set(t,a),n}function es(e,t,i,n,a,r){ee(e,t,a=new a((i=0===i.length&&0==n.byteOffset%r?n:eo(i,n)).buffer,i.byteOffset,i.byteLength/r))}function el(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.")}function ec(e){return new Z(e.serverConsumerManifest.moduleMap,e.serverConsumerManifest.serverModuleMap,e.serverConsumerManifest.moduleLoading,el,e.encodeFormAction,"string"==typeof e.nonce?e.nonce:void 0,e&&e.temporaryReferences?e.temporaryReferences:void 0)}function ep(e,t){function i(t){U(e,t)}var n=t.getReader();n.read().then(function t(r){var o=r.value;if(r.done)U(e,Error("Connection closed."));else{var s=0,c=e._rowState;r=e._rowID;for(var d=e._rowTag,u=e._rowLength,m=e._buffer,h=o.length;s<h;){var f=-1;switch(c){case 0:58===(f=o[s++])?c=1:r=r<<4|(96<f?f-87:f-48);continue;case 1:84===(c=o[s])||65===c||79===c||111===c||85===c||83===c||115===c||76===c||108===c||71===c||103===c||77===c||109===c||86===c?(d=c,c=2,s++):64<c&&91>c||35===c||114===c||120===c?(d=c,c=3,s++):(d=0,c=3);continue;case 2:44===(f=o[s++])?c=4:u=u<<4|(96<f?f-87:f-48);continue;case 3:f=o.indexOf(10,s);break;case 4:(f=s+u)>o.length&&(f=-1)}var g=o.byteOffset+s;if(-1<f)(function(e,t,i,n,r){switch(i){case 65:ee(e,t,eo(n,r).buffer);return;case 79:es(e,t,n,r,Int8Array,1);return;case 111:ee(e,t,0===n.length?r:eo(n,r));return;case 85:es(e,t,n,r,Uint8ClampedArray,1);return;case 83:es(e,t,n,r,Int16Array,2);return;case 115:es(e,t,n,r,Uint16Array,2);return;case 76:es(e,t,n,r,Int32Array,4);return;case 108:es(e,t,n,r,Uint32Array,4);return;case 71:es(e,t,n,r,Float32Array,4);return;case 103:es(e,t,n,r,Float64Array,8);return;case 77:es(e,t,n,r,BigInt64Array,8);return;case 109:es(e,t,n,r,BigUint64Array,8);return;case 86:es(e,t,n,r,DataView,1);return}for(var o=e._stringDecoder,s="",c=0;c<n.length;c++)s+=o.decode(n[c],a);switch(n=s+=o.decode(r),i){case 73:var d=e,u=t,m=n,h=d._chunks,f=h.get(u);m=JSON.parse(m,d._fromJSON);var g=function(e,t){if(e){var i=e[t[0]];if(e=i&&i[t[2]])i=e.name;else{if(!(e=i&&i["*"]))throw Error('Could not find the module "'+t[0]+'" in the React Server Consumer Manifest. This is probably a bug in the React Server Components bundler.');i=t[2]}return 4===t.length?[e.id,e.chunks,i,1]:[e.id,e.chunks,i]}return t}(d._bundlerConfig,m);if(!function(e,t,i){if(null!==e)for(var n=1;n<t.length;n+=2){var a=p.d,r=a.X,o=e.prefix+t[n],s=e.crossOrigin;s="string"==typeof s?"use-credentials"===s?s:"":void 0,r.call(a,o,{crossOrigin:s,nonce:i})}}(d._moduleLoading,m[1],d._nonce),m=l(g)){if(f){var v=f;v.status="blocked"}else v=new A("blocked",null,null,d),h.set(u,v);m.then(function(){return L(v,g)},function(e){return R(v,e)})}else f?L(f,g):h.set(u,new A("resolved_module",g,null,d));break;case 72:switch(t=n[0],e=JSON.parse(n=n.slice(1),e._fromJSON),n=p.d,t){case"D":n.D(e);break;case"C":"string"==typeof e?n.C(e):n.C(e[0],e[1]);break;case"L":t=e[0],i=e[1],3===e.length?n.L(t,i,e[2]):n.L(t,i);break;case"m":"string"==typeof e?n.m(e):n.m(e[0],e[1]);break;case"X":"string"==typeof e?n.X(e):n.X(e[0],e[1]);break;case"S":"string"==typeof e?n.S(e):n.S(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case"M":"string"==typeof e?n.M(e):n.M(e[0],e[1])}break;case 69:i=JSON.parse(n),(n=er()).digest=i.digest,(r=(i=e._chunks).get(t))?R(r,n):i.set(t,new A("rejected",null,n,e));break;case 84:(r=(i=e._chunks).get(t))&&"pending"!==r.status?r.reason.enqueueValue(n):i.set(t,new A("fulfilled",n,null,e));break;case 78:case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");case 82:ei(e,t,void 0);break;case 114:ei(e,t,"bytes");break;case 88:ea(e,t,!1);break;case 120:ea(e,t,!0);break;case 67:(e=e._chunks.get(t))&&"fulfilled"===e.status&&e.reason.close(""===n?'"$undefined"':n);break;default:(r=(i=e._chunks).get(t))?I(r,n):i.set(t,new A("resolved_model",n,null,e))}})(e,r,d,m,u=new Uint8Array(o.buffer,g,f-s)),s=f,3===c&&s++,u=r=d=c=0,m.length=0;else{o=new Uint8Array(o.buffer,g,o.byteLength-s),m.push(o),u-=o.byteLength;break}}return e._rowState=c,e._rowID=r,e._rowTag=d,e._rowLength=u,n.read().then(t).catch(i)}}).catch(i)}t.createFromFetch=function(e,t){var i=ec(t);return e.then(function(e){ep(i,e.body)},function(e){U(i,e)}),z(i,0)},t.createFromReadableStream=function(e,t){return ep(t=ec(t),e),z(t,0)},t.createServerReference=function(e){function t(){var t=Array.prototype.slice.call(arguments);return el(e,t)}return E(t,e,null,void 0),t},t.createTemporaryReferenceSet=function(){return new Map},t.encodeReply=function(e,t){return new Promise(function(i,n){var a=b(e,"",t&&t.temporaryReferences?t.temporaryReferences:void 0,i,n);if(t&&t.signal){var r=t.signal;if(r.aborted)a(r.reason);else{var o=function(){a(r.reason),r.removeEventListener("abort",o)};r.addEventListener("abort",o)}}})},t.registerServerReference=function(e,t,i){return E(e,t,null,i),e}},37294:(e,t,i)=>{"use strict";let n=i(94735),a=i(12501),r=i(58812),o=i(89581),s=i(1573),l=i(91423);class c extends n{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=o.parseConnectionUrl(e.url),i=i||t.service),this.options=o.assign(!1,e,t,i&&r(i)),this.logger=o.getLogger(this.options,{component:this.options.component||"smtp-transport"});let n=new a(this.options);this.name="SMTP",this.version=l.version+"[client:"+n.version+"]",this.options.auth&&(this.auth=this.getAuth({}))}getSocket(e,t){return setImmediate(()=>t(null,!1))}getAuth(e){if(!e)return this.auth;let t=!1,i={};if(this.options.auth&&"object"==typeof this.options.auth&&Object.keys(this.options.auth).forEach(e=>{t=!0,i[e]=this.options.auth[e]}),e&&"object"==typeof e&&Object.keys(e).forEach(n=>{t=!0,i[n]=e[n]}),!t)return!1;if("OAUTH2"!==(i.type||"").toString().toUpperCase())return{type:(i.type||"").toString().toUpperCase()||"LOGIN",user:i.user,credentials:{user:i.user||"",pass:i.pass,options:i.options},method:(i.method||"").trim().toUpperCase()||this.options.authMethod||!1};{if(!i.service&&!i.user)return!1;let e=new s(i,this.logger);return e.provisionCallback=this.mailer&&this.mailer.get("oauth2_provision_cb")||e.provisionCallback,e.on("token",e=>this.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e)),{type:"OAUTH2",user:i.user,oauth2:e,method:"XOAUTH2"}}}send(e,t){this.getSocket(this.options,(i,n)=>{if(i)return t(i);let r=!1,s=this.options;n&&n.connection&&(this.logger.info({tnx:"proxy",remoteAddress:n.connection.remoteAddress,remotePort:n.connection.remotePort,destHost:s.host||"",destPort:s.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",n.connection.remoteAddress,n.connection.remotePort,s.host||"",s.port||""),s=o.assign(!1,s),Object.keys(n).forEach(e=>{s[e]=n[e]}));let l=new a(s);l.once("error",e=>{if(!r)return r=!0,l.close(),t(e)}),l.once("end",()=>{if(r)return;let e=setTimeout(()=>{if(r)return;r=!0;let e=Error("Unexpected socket close");l&&l._socket&&l._socket.upgrading&&(e.code="ETLS"),t(e)},1e3);try{e.unref()}catch(e){}});let c=()=>{let i=e.message.getEnvelope(),n=e.message.messageId(),a=[].concat(i.to||[]);a.length>3&&a.push("...and "+a.splice(2).length+" more"),e.data.dsn&&(i.dsn=e.data.dsn),this.logger.info({tnx:"send",messageId:n},"Sending message %s to <%s>",n,a.join(", ")),l.send(i,e.message.createReadStream(),(e,a)=>{if(r=!0,l.close(),e)return this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",n,e.message),t(e);a.envelope={from:i.from,to:i.to},a.messageId=n;try{return t(null,a)}catch(e){this.logger.error({err:e,tnx:"callback"},"Callback error for %s: %s",n,e.message)}})};l.connect(()=>{if(r)return;let i=this.getAuth(e.data.auth);i&&(l.allowsAuth||s.forceAuth)?l.login(i,e=>{if(i&&i!==this.auth&&i.oauth2&&i.oauth2.removeAllListeners(),!r){if(e)return r=!0,l.close(),t(e);c()}}):c()})})}verify(e){let t;return e||(t=new Promise((t,i)=>{e=o.callbackPromise(t,i)})),this.getSocket(this.options,(t,i)=>{if(t)return e(t);let n=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:n.host||"",destPort:n.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,n.host||"",n.port||""),n=o.assign(!1,n),Object.keys(i).forEach(e=>{n[e]=i[e]}));let r=new a(n),s=!1;r.once("error",t=>{if(!s)return s=!0,r.close(),e(t)}),r.once("end",()=>{if(!s)return s=!0,e(Error("Connection closed"))});let l=()=>{if(!s)return s=!0,r.quit(),e(null,!0)};r.connect(()=>{if(s)return;let t=this.getAuth({});if(t&&(r.allowsAuth||n.forceAuth))r.login(t,t=>{if(!s){if(t)return s=!0,r.close(),e(t);l()}});else if(!t&&r.allowsAuth&&n.forceAuth){let t=Error("Authentication info was not provided");return t.code="NoAuth",s=!0,r.close(),e(t)}else l()})}),t}close(){this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.emit("close")}}e.exports=c},37366:e=>{"use strict";e.exports=require("dns")},37461:(e,t)=>{"use strict";function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{isHangingPromiseRejectionError:function(){return i},makeHangingPromise:function(){return o}});let n="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=n}}let r=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new a(t));{let i=new Promise((i,n)=>{let o=n.bind(null,new a(t)),s=r.get(e);if(s)s.push(o);else{let t=[o];r.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return i.catch(s),i}}function s(){}},38248:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return a}});let i="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=i}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===i}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38424:(e,t,i)=>{"use strict";let n=i(27910).Transform;class a extends n{constructor(e){super(e),this.options=e||{},this._curLine="",this.inByteCount=0,this.outByteCount=0,this.lastByte=!1}_transform(e,t,i){let n,a=[],r=0,o,s,l=0;if(!e||!e.length)return i();for("string"==typeof e&&(e=Buffer.from(e)),this.inByteCount+=e.length,o=0,s=e.length;o<s;o++)46===e[o]?(!o||10!==e[o-1])&&(o||this.lastByte&&10!==this.lastByte)||(n=e.slice(l,o+1),a.push(n),a.push(Buffer.from(".")),r+=n.length+1,l=o+1):10===e[o]&&(o&&13!==e[o-1]||!o&&13!==this.lastByte)&&(o>l?(n=e.slice(l,o),a.push(n),r+=n.length+2):r+=2,a.push(Buffer.from("\r\n")),l=o+1);r?(l<e.length&&(n=e.slice(l),a.push(n),r+=n.length),this.outByteCount+=r,this.push(Buffer.concat(a,r))):(this.outByteCount+=e.length,this.push(e)),this.lastByte=e[e.length-1],i()}_flush(e){let t;t=10===this.lastByte?Buffer.from(".\r\n"):13===this.lastByte?Buffer.from("\n.\r\n"):Buffer.from("\r\n.\r\n"),this.outByteCount+=t.length,this.push(t),e()}}e.exports=a},39358:(e,t,i)=>{"use strict";let n=i(27910).Transform;class a extends n{constructor(e){super(e),this.options=e||{},this.lastByte=!1}_transform(e,t,i){let n,a=0;for(let t=0,i=e.length;t<i;t++)10===e[t]&&(t&&13!==e[t-1]||!t&&13!==this.lastByte)&&(t>a&&(n=e.slice(a,t),this.push(n)),this.push(Buffer.from("\r\n")),a=t+1);a&&a<e.length?(n=e.slice(a),this.push(n)):a||this.push(e),this.lastByte=e[e.length-1],i()}}e.exports=a},41635:(e,t,i)=>{"use strict";let n=i(18982),a=i(89581),r=i(27207),o=i(37294),s=i(85909),l=i(96002),c=i(53312),p=i(66647),d=i(29564),u=i(91423),m=(process.env.ETHEREAL_API||"https://api.nodemailer.com").replace(/\/+$/,""),h=(process.env.ETHEREAL_WEB||"https://ethereal.email").replace(/\/+$/,""),f=(process.env.ETHEREAL_API_KEY||"").replace(/\s*/g,"")||null,g=["true","yes","y","1"].includes((process.env.ETHEREAL_CACHE||"yes").toString().trim().toLowerCase()),v=!1;e.exports.createTransport=function(e,t){let i,d;if("object"==typeof e&&"function"!=typeof e.send||"string"==typeof e&&/^(smtps?|direct):/i.test(e))if((d=(i="string"==typeof e?e:e.url)?a.parseConnectionUrl(i):e).pool)e=new r(d);else if(d.sendmail)e=new s(d);else if(d.streamTransport)e=new l(d);else if(d.jsonTransport)e=new c(d);else if(d.SES){if(d.SES.ses&&d.SES.aws){let e=Error("Using legacy SES configuration, expecting @aws-sdk/client-sesv2, see https://nodemailer.com/transports/ses/");throw e.code="LegacyConfig",e}e=new p(d)}else e=new o(d);return new n(e,d,t)},e.exports.createTestAccount=function(e,t){let i;if(t||"function"!=typeof e||(t=e,e=!1),t||(i=new Promise((e,i)=>{t=a.callbackPromise(e,i)})),g&&v)return setImmediate(()=>t(null,v)),i;e=e||m;let n=[],r=0,o={},s={requestor:u.name,version:u.version};f&&(o.Authorization="Bearer "+f);let l=d(e+"/user",{contentType:"application/json",method:"POST",headers:o,body:Buffer.from(JSON.stringify(s))});return l.on("readable",()=>{let e;for(;null!==(e=l.read());)n.push(e),r+=e.length}),l.once("error",e=>t(e)),l.once("end",()=>{let e,i,a=Buffer.concat(n,r);try{e=JSON.parse(a.toString())}catch(e){i=e}return i?t(i):"success"!==e.status||e.error?t(Error(e.error||"Request failed")):void(delete e.status,t(null,v=e))}),i},e.exports.getTestMessageUrl=function(e){if(!e||!e.response)return!1;let t=new Map;return e.response.replace(/\[([^\]]+)\]$/,(e,i)=>{i.replace(/\b([A-Z0-9]+)=([^\s]+)/g,(e,i,n)=>{t.set(i,n)})}),!!(t.has("STATUS")&&t.has("MSGID"))&&(v.web||h)+"/message/"+t.get("MSGID")}},41957:e=>{"use strict";let t=/^xn--/,i=/[^\0-\x7F]/,n=/[\x2E\u3002\uFF0E\uFF61]/g,a={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},r=Math.floor,o=String.fromCharCode;function s(e){throw RangeError(a[e])}function l(e,t){let i=e.split("@"),a="";return i.length>1&&(a=i[0]+"@",e=i[1]),a+(function(e,t){let i=[],n=e.length;for(;n--;)i[n]=t(e[n]);return i})((e=e.replace(n,".")).split("."),t).join(".")}function c(e){let t=[],i=0,n=e.length;for(;i<n;){let a=e.charCodeAt(i++);if(a>=55296&&a<=56319&&i<n){let n=e.charCodeAt(i++);(64512&n)==56320?t.push(((1023&a)<<10)+(1023&n)+65536):(t.push(a),i--)}else t.push(a)}return t}let p=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},d=function(e,t,i){let n=0;for(e=i?r(e/700):e>>1,e+=r(e/t);e>455;n+=36)e=r(e/35);return r(n+36*e/(e+38))},u=function(e){let t=[],i=e.length,n=0,a=128,o=72,l=e.lastIndexOf("-");l<0&&(l=0);for(let i=0;i<l;++i)e.charCodeAt(i)>=128&&s("not-basic"),t.push(e.charCodeAt(i));for(let p=l>0?l+1:0;p<i;){let l=n;for(let t=1,a=36;;a+=36){var c;p>=i&&s("invalid-input");let l=(c=e.charCodeAt(p++))>=48&&c<58?26+(c-48):c>=65&&c<91?c-65:c>=97&&c<123?c-97:36;l>=36&&s("invalid-input"),l>r((0x7fffffff-n)/t)&&s("overflow"),n+=l*t;let d=a<=o?1:a>=o+26?26:a-o;if(l<d)break;let u=36-d;t>r(0x7fffffff/u)&&s("overflow"),t*=u}let u=t.length+1;o=d(n-l,u,0==l),r(n/u)>0x7fffffff-a&&s("overflow"),a+=r(n/u),n%=u,t.splice(n++,0,a)}return String.fromCodePoint(...t)},m=function(e){let t=[],i=(e=c(e)).length,n=128,a=0,l=72;for(let i of e)i<128&&t.push(o(i));let u=t.length,m=u;for(u&&t.push("-");m<i;){let i=0x7fffffff;for(let t of e)t>=n&&t<i&&(i=t);let c=m+1;for(let h of(i-n>r((0x7fffffff-a)/c)&&s("overflow"),a+=(i-n)*c,n=i,e))if(h<n&&++a>0x7fffffff&&s("overflow"),h===n){let e=a;for(let i=36;;i+=36){let n=i<=l?1:i>=l+26?26:i-l;if(e<n)break;let a=e-n,s=36-n;t.push(o(p(n+a%s,0))),e=r(a/s)}t.push(o(p(e,0))),l=d(a,c,m===u),a=0,++m}++a,++n}return t.join("")};e.exports={version:"2.3.1",ucs2:{decode:c,encode:e=>String.fromCodePoint(...e)},decode:u,encode:m,toASCII:function(e){return l(e,function(e){return i.test(e)?"xn--"+m(e):e})},toUnicode:function(e){return l(e,function(e){return t.test(e)?u(e.slice(4).toLowerCase()):e})}}},42087:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{decryptActionBoundArgs:function(){return f},encryptActionBoundArgs:function(){return h}}),i(54387);let n=i(74932),a=i(64390),r=i(34360),o=i(46295),s=i(63033),l=i(75124),c=function(e){return e&&e.__esModule?e:{default:e}}(i(7153)),p=new TextEncoder,d=new TextDecoder;async function u(e,t){let i=await (0,o.getActionEncryptionKey)();if(void 0===i)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(t),a=n.slice(0,16),r=n.slice(16),s=d.decode(await (0,o.decrypt)(i,(0,o.stringToUint8Array)(a),(0,o.stringToUint8Array)(r)));if(!s.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return s.slice(e.length)}async function m(e,t){let i=await (0,o.getActionEncryptionKey)();if(void 0===i)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);s.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let a=(0,o.arrayBufferToString)(n.buffer),r=await (0,o.encrypt)(i,n,p.encode(e+t));return btoa(a+(0,o.arrayBufferToString)(r))}let h=c.default.cache(async function e(t,...i){let{clientModules:a}=(0,o.getClientReferenceManifestForRsc)(),c=Error();Error.captureStackTrace(c,e);let p=!1,d=s.workUnitAsyncStorage.getStore(),u=(null==d?void 0:d.type)==="prerender"?(0,l.createHangingInputAbortSignal)(d):void 0,h=await (0,r.streamToString)((0,n.renderToReadableStream)(i,a,{signal:u,onError(e){(null==u||!u.aborted)&&(p||(p=!0,c.message=e instanceof Error?e.message:String(e)))}}),u);if(p)throw c;if(!d)return m(t,h);let f=(0,s.getPrerenderResumeDataCache)(d),g=(0,s.getRenderResumeDataCache)(d),v=t+h,x=(null==f?void 0:f.encryptedBoundArgs.get(v))??(null==g?void 0:g.encryptedBoundArgs.get(v));if(x)return x;let b="prerender"===d.type?d.cacheSignal:void 0;null==b||b.beginRead();let y=await m(t,h);return null==b||b.endRead(),null==f||f.encryptedBoundArgs.set(v,y),y});async function f(e,t){let i,n=await t,r=s.workUnitAsyncStorage.getStore();if(r){let t="prerender"===r.type?r.cacheSignal:void 0,a=(0,s.getPrerenderResumeDataCache)(r),o=(0,s.getRenderResumeDataCache)(r);(i=(null==a?void 0:a.decryptedBoundArgs.get(n))??(null==o?void 0:o.decryptedBoundArgs.get(n)))||(null==t||t.beginRead(),i=await u(e,n),null==t||t.endRead(),null==a||a.decryptedBoundArgs.set(n,i))}else i=await u(e,n);let{edgeRscModuleMapping:l,rscModuleMapping:c}=(0,o.getClientReferenceManifestForRsc)();return await (0,a.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(p.encode(i)),(null==r?void 0:r.type)==="prerender"?r.renderSignal.aborted?e.close():r.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:c,serverModuleMap:(0,o.getServerModuleMap)()}})}},42503:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ENCODED_TAGS",{enumerable:!0,get:function(){return i}});let i={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},43839:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});let n=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\contact\\page.tsx","default")},44334:(e,t,i)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{BubbledError:function(){return u},SpanKind:function(){return p},SpanStatusCode:function(){return c},getTracer:function(){return _},isBubbledError:function(){return m}});let a=i(2720),r=i(23879);try{n=i(73214)}catch(e){n=i(73214)}let{context:o,propagation:s,trace:l,SpanStatusCode:c,SpanKind:p,ROOT_CONTEXT:d}=n;class u extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}function m(e){return"object"==typeof e&&null!==e&&e instanceof u}let h=(e,t)=>{m(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:c.ERROR,message:null==t?void 0:t.message})),e.end()},f=new Map,g=n.createContextKey("next.rootSpanId"),v=0,x=()=>v++,b={set(e,t,i){e.push({key:t,value:i})}};class y{getTracerInstance(){return l.getTracer("next.js","0.0.1")}getContext(){return o}getTracePropagationData(){let e=o.active(),t=[];return s.inject(e,t,b),t}getActiveScopeSpan(){return l.getSpan(null==o?void 0:o.active())}withPropagatedContext(e,t,i){let n=o.active();if(l.getSpanContext(n))return t();let a=s.extract(n,e,i);return o.with(a,t)}trace(...e){var t;let[i,n,s]=e,{fn:c,options:p}="function"==typeof n?{fn:n,options:{}}:{fn:s,options:{...n}},u=p.spanName??i;if(!a.NextVanillaSpanAllowlist.includes(i)&&"1"!==process.env.NEXT_OTEL_VERBOSE||p.hideSpan)return c();let m=this.getSpanContext((null==p?void 0:p.parentSpan)??this.getActiveScopeSpan()),v=!1;m?(null==(t=l.getSpanContext(m))?void 0:t.isRemote)&&(v=!0):(m=(null==o?void 0:o.active())??d,v=!0);let b=x();return p.attributes={"next.span_name":u,"next.span_type":i,...p.attributes},o.with(m.setValue(g,b),()=>this.getTracerInstance().startActiveSpan(u,p,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{f.delete(b),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&a.LogSpanAllowList.includes(i||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(i.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};v&&f.set(b,new Map(Object.entries(p.attributes??{})));try{if(c.length>1)return c(e,t=>h(e,t));let t=c(e);if((0,r.isThenable)(t))return t.then(t=>(e.end(),t)).catch(t=>{throw h(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw h(e,t),n(),t}}))}wrap(...e){let t=this,[i,n,r]=3===e.length?e:[e[0],{},e[1]];return a.NextVanillaSpanAllowlist.includes(i)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof r&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(i,e,()=>r.apply(this,arguments));{let n=t.getContext().bind(o.active(),s);return t.trace(i,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},r.apply(this,arguments)))}}:r}startSpan(...e){let[t,i]=e,n=this.getSpanContext((null==i?void 0:i.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,i,n)}getSpanContext(e){return e?l.setSpan(o.active(),e):void 0}getRootSpanAttributes(){let e=o.active().getValue(g);return f.get(e)}setRootSpanAttribute(e,t){let i=o.active().getValue(g),n=f.get(i);n&&n.set(e,t)}}let _=(()=>{let e=new y;return()=>e})()},44537:(e,t,i)=>{"use strict";let n=i(89581),a=i(48477),r=i(34e3);class o{constructor(e,t){this.mailer=e,this.data={},this.message=null,t=t||{};let i=e.options||{},n=e._defaults||{};Object.keys(t).forEach(e=>{this.data[e]=t[e]}),this.data.headers=this.data.headers||{},Object.keys(n).forEach(e=>{e in this.data?"headers"===e&&Object.keys(n.headers).forEach(e=>{e in this.data.headers||(this.data.headers[e]=n.headers[e])}):this.data[e]=n[e]}),["disableFileAccess","disableUrlAccess","normalizeHeaderKey"].forEach(e=>{e in i&&(this.data[e]=i[e])})}resolveContent(...e){return n.resolveContent(...e)}resolveAll(e){let t=[[this.data,"html"],[this.data,"text"],[this.data,"watchHtml"],[this.data,"amp"],[this.data,"icalEvent"]];this.data.alternatives&&this.data.alternatives.length&&this.data.alternatives.forEach((e,i)=>{t.push([this.data.alternatives,i])}),this.data.attachments&&this.data.attachments.length&&this.data.attachments.forEach((e,i)=>{!e.filename&&(e.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(i+1),0>e.filename.indexOf(".")&&(e.filename+="."+r.detectExtension(e.contentType))),e.contentType||(e.contentType=r.detectMimeType(e.filename||e.path||e.href||"bin")),t.push([this.data.attachments,i])});let i=new a;["from","to","cc","bcc","sender","replyTo"].forEach(e=>{let t;this.message?t=[].concat(i._parseAddresses(this.message.getHeader("replyTo"===e?"reply-to":e))||[]):this.data[e]&&(t=[].concat(i._parseAddresses(this.data[e])||[])),t&&t.length?this.data[e]=t:e in this.data&&(this.data[e]=null)}),["from","sender"].forEach(e=>{this.data[e]&&(this.data[e]=this.data[e].shift())});let o=0,s=()=>{if(o>=t.length)return e(null,this.data);let i=t[o++];if(!i[0]||!i[0][i[1]])return s();n.resolveContent(...i,(t,n)=>{if(t)return e(t);let a={content:n};i[0][i[1]]&&"object"==typeof i[0][i[1]]&&!Buffer.isBuffer(i[0][i[1]])&&Object.keys(i[0][i[1]]).forEach(e=>{e in a||["content","path","href","raw"].includes(e)||(a[e]=i[0][i[1]][e])}),i[0][i[1]]=a,s()})};setImmediate(()=>s())}normalize(e){let t=this.data.envelope||this.message.getEnvelope(),i=this.message.messageId();this.resolveAll((n,a)=>n?e(n):(a.envelope=t,a.messageId=i,["html","text","watchHtml","amp"].forEach(e=>{a[e]&&a[e].content&&("string"==typeof a[e].content?a[e]=a[e].content:Buffer.isBuffer(a[e].content)&&(a[e]=a[e].content.toString()))}),a.icalEvent&&Buffer.isBuffer(a.icalEvent.content)&&(a.icalEvent.content=a.icalEvent.content.toString("base64"),a.icalEvent.encoding="base64"),a.alternatives&&a.alternatives.length&&a.alternatives.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),a.attachments&&a.attachments.length&&a.attachments.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),a.normalizedHeaders={},Object.keys(a.headers||{}).forEach(e=>{let t=[].concat(a.headers[e]||[]).shift();(t=t&&t.value||t)&&(["references","in-reply-to","message-id","content-id"].includes(e)&&(t=this.message._encodeHeaderValue(e,t)),a.normalizedHeaders[e]=t)}),a.list&&"object"==typeof a.list&&this._getListHeaders(a.list).forEach(e=>{a.normalizedHeaders[e.key]=e.value.map(e=>e&&e.value||e).join(", ")}),a.references&&(a.normalizedHeaders.references=this.message._encodeHeaderValue("references",a.references)),a.inReplyTo&&(a.normalizedHeaders["in-reply-to"]=this.message._encodeHeaderValue("in-reply-to",a.inReplyTo)),e(null,a)))}setMailerHeader(){this.message&&this.data.xMailer&&this.message.setHeader("X-Mailer",this.data.xMailer)}setPriorityHeaders(){if(this.message&&this.data.priority)switch((this.data.priority||"").toString().toLowerCase()){case"high":this.message.setHeader("X-Priority","1 (Highest)"),this.message.setHeader("X-MSMail-Priority","High"),this.message.setHeader("Importance","High");break;case"low":this.message.setHeader("X-Priority","5 (Lowest)"),this.message.setHeader("X-MSMail-Priority","Low"),this.message.setHeader("Importance","Low")}}setListHeaders(){this.message&&this.data.list&&"object"==typeof this.data.list&&this.data.list&&"object"==typeof this.data.list&&this._getListHeaders(this.data.list).forEach(e=>{e.value.forEach(t=>{this.message.addHeader(e.key,t)})})}_getListHeaders(e){return Object.keys(e).map(t=>({key:"list-"+t.toLowerCase().trim(),value:[].concat(e[t]||[]).map(e=>({prepared:!0,foldLines:!0,value:[].concat(e||[]).map(e=>{if("string"==typeof e&&(e={url:e}),e&&e.url){if("id"===t.toLowerCase().trim()){let t=e.comment||"";return t=r.isPlainText(t)?'"'+t+'"':r.encodeWord(t),(e.comment?t+" ":"")+this._formatListUrl(e.url).replace(/^<[^:]+\/{,2}/,"")}let i=e.comment||"";return r.isPlainText(i)||(i=r.encodeWord(i)),this._formatListUrl(e.url)+(e.comment?" ("+i+")":"")}return""}).filter(e=>e).join(", ")}))}))}_formatListUrl(e){return(e=e.replace(/[\s<]+|[\s>]+/g,""),/^(https?|mailto|ftp):/.test(e))?"<"+e+">":/^[^@]+@[^@]+$/.test(e)?"<mailto:"+e+">":"<http://"+e+">"}}e.exports=o},44992:(e,t,i)=>{"use strict";let n=i(48477),a=i(34e3),r=i(89581).parseDataURI;class o{constructor(e){this.mail=e||{},this.message=!1}compile(){return this._alternatives=this.getAlternatives(),this._htmlNode=this._alternatives.filter(e=>/^text\/html\b/i.test(e.contentType)).pop(),this._attachments=this.getAttachments(!!this._htmlNode),this._useRelated=!!(this._htmlNode&&this._attachments.related.length),this._useAlternative=this._alternatives.length>1,this._useMixed=this._attachments.attached.length>1||this._alternatives.length&&1===this._attachments.attached.length,this.mail.raw?this.message=new n("message/rfc822",{newline:this.mail.newline}).setRaw(this.mail.raw):this._useMixed?this.message=this._createMixed():this._useAlternative?this.message=this._createAlternative():this._useRelated?this.message=this._createRelated():this.message=this._createContentNode(!1,[].concat(this._alternatives||[]).concat(this._attachments.attached||[]).shift()||{contentType:"text/plain",content:""}),this.mail.headers&&this.message.addHeader(this.mail.headers),["from","sender","to","cc","bcc","reply-to","in-reply-to","references","subject","message-id","date"].forEach(e=>{let t=e.replace(/-(\w)/g,(e,t)=>t.toUpperCase());this.mail[t]&&this.message.setHeader(e,this.mail[t])}),this.mail.envelope&&this.message.setEnvelope(this.mail.envelope),this.message.messageId(),this.message}getAttachments(e){let t,i,n=[].concat(this.mail.attachments||[]).map((e,t)=>{let i,n;/^data:/i.test(e.path||e.href)&&(e=this._processDataUrl(e));let r=e.contentType||a.detectMimeType(e.filename||e.path||e.href||"bin"),o=/^image\//i.test(r),s=/^message\//i.test(r);return i={contentType:r,contentDisposition:e.contentDisposition||(s||o&&e.cid?"inline":"attachment"),contentTransferEncoding:"contentTransferEncoding"in e?e.contentTransferEncoding:s?"7bit":"base64"},e.filename?i.filename=e.filename:!s&&!1!==e.filename&&(i.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(t+1),0>i.filename.indexOf(".")&&(i.filename+="."+a.detectExtension(i.contentType))),/^https?:\/\//i.test(e.path)&&(e.href=e.path,e.path=void 0),e.cid&&(i.cid=e.cid),e.raw?i.raw=e.raw:e.path?i.content={path:e.path}:e.href?i.content={href:e.href,httpHeaders:e.httpHeaders}:i.content=e.content||"",e.encoding&&(i.encoding=e.encoding),e.headers&&(i.headers=e.headers),i});return(this.mail.icalEvent&&(t="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},i={},Object.keys(t).forEach(e=>{i[e]=t[e]}),i.contentType="application/ics",i.headers||(i.headers={}),i.filename=i.filename||"invite.ics",i.headers["Content-Disposition"]="attachment",i.headers["Content-Transfer-Encoding"]="base64"),e)?{attached:n.filter(e=>!e.cid).concat(i||[]),related:n.filter(e=>!!e.cid)}:{attached:n.concat(i||[]),related:[]}}getAlternatives(){let e=[],t,i,n,r,o,s;return this.mail.text&&((t="object"==typeof this.mail.text&&(this.mail.text.content||this.mail.text.path||this.mail.text.href||this.mail.text.raw)?this.mail.text:{content:this.mail.text}).contentType="text/plain; charset=utf-8"),this.mail.watchHtml&&((n="object"==typeof this.mail.watchHtml&&(this.mail.watchHtml.content||this.mail.watchHtml.path||this.mail.watchHtml.href||this.mail.watchHtml.raw)?this.mail.watchHtml:{content:this.mail.watchHtml}).contentType="text/watch-html; charset=utf-8"),this.mail.amp&&((r="object"==typeof this.mail.amp&&(this.mail.amp.content||this.mail.amp.path||this.mail.amp.href||this.mail.amp.raw)?this.mail.amp:{content:this.mail.amp}).contentType="text/x-amp-html; charset=utf-8"),this.mail.icalEvent&&(o="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},s={},Object.keys(o).forEach(e=>{s[e]=o[e]}),s.content&&"object"==typeof s.content&&(s.content._resolve=!0),s.filename=!1,s.contentType="text/calendar; charset=utf-8; method="+(s.method||"PUBLISH").toString().trim().toUpperCase(),s.headers||(s.headers={})),this.mail.html&&((i="object"==typeof this.mail.html&&(this.mail.html.content||this.mail.html.path||this.mail.html.href||this.mail.html.raw)?this.mail.html:{content:this.mail.html}).contentType="text/html; charset=utf-8"),[].concat(t||[]).concat(n||[]).concat(r||[]).concat(i||[]).concat(s||[]).concat(this.mail.alternatives||[]).forEach(t=>{let i;/^data:/i.test(t.path||t.href)&&(t=this._processDataUrl(t)),i={contentType:t.contentType||a.detectMimeType(t.filename||t.path||t.href||"txt"),contentTransferEncoding:t.contentTransferEncoding},t.filename&&(i.filename=t.filename),/^https?:\/\//i.test(t.path)&&(t.href=t.path,t.path=void 0),t.raw?i.raw=t.raw:t.path?i.content={path:t.path}:t.href?i.content={href:t.href}:i.content=t.content||"",t.encoding&&(i.encoding=t.encoding),t.headers&&(i.headers=t.headers),e.push(i)}),e}_createMixed(e){let t;return t=e?e.createChild("multipart/mixed",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new n("multipart/mixed",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._useAlternative?this._createAlternative(t):this._useRelated&&this._createRelated(t),[].concat(!this._useAlternative&&this._alternatives||[]).concat(this._attachments.attached||[]).forEach(e=>{this._useRelated&&e===this._htmlNode||this._createContentNode(t,e)}),t}_createAlternative(e){let t;return t=e?e.createChild("multipart/alternative",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new n("multipart/alternative",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._alternatives.forEach(e=>{this._useRelated&&this._htmlNode===e?this._createRelated(t):this._createContentNode(t,e)}),t}_createRelated(e){let t;return t=e?e.createChild('multipart/related; type="text/html"',{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new n('multipart/related; type="text/html"',{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._createContentNode(t,this._htmlNode),this._attachments.related.forEach(e=>this._createContentNode(t,e)),t}_createContentNode(e,t){let i;(t=t||{}).content=t.content||"";let a=(t.encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");return i=e?e.createChild(t.contentType,{filename:t.filename,textEncoding:this.mail.textEncoding,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new n(t.contentType,{filename:t.filename,baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),t.headers&&i.addHeader(t.headers),t.cid&&i.setHeader("Content-Id","<"+t.cid.replace(/[<>]/g,"")+">"),t.contentTransferEncoding?i.setHeader("Content-Transfer-Encoding",t.contentTransferEncoding):this.mail.encoding&&/^text\//i.test(t.contentType)&&i.setHeader("Content-Transfer-Encoding",this.mail.encoding),(!/^text\//i.test(t.contentType)||t.contentDisposition)&&i.setHeader("Content-Disposition",t.contentDisposition||(t.cid&&/^image\//i.test(t.contentType)?"inline":"attachment")),"string"!=typeof t.content||["utf8","usascii","ascii"].includes(a)||(t.content=Buffer.from(t.content,a)),t.raw?i.setRaw(t.raw):i.setContent(t.content),i}_processDataUrl(e){let t;return(e.path||e.href).match(/^data:/)&&(t=r(e.path||e.href)),t&&(e.content=t.data,e.contentType=e.contentType||t.contentType,"path"in e&&(e.path=!1),"href"in e&&(e.href=!1)),e}}e.exports=o},46295:(e,t,i)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{arrayBufferToString:function(){return s},decrypt:function(){return p},encrypt:function(){return c},getActionEncryptionKey:function(){return f},getClientReferenceManifestForRsc:function(){return h},getServerModuleMap:function(){return m},setReferenceManifestsSingleton:function(){return u},stringToUint8Array:function(){return l}});let a=i(23302),r=i(79535),o=i(29294);function s(e){let t=new Uint8Array(e),i=t.byteLength;if(i<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<i;e++)n+=String.fromCharCode(t[e]);return n}function l(e){let t=e.length,i=new Uint8Array(t);for(let n=0;n<t;n++)i[n]=e.charCodeAt(n);return i}function c(e,t,i){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,i)}function p(e,t,i){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,i)}let d=Symbol.for("next.server.action-manifests");function u({page:e,clientReferenceManifest:t,serverActionsManifest:i,serverModuleMap:n}){var a;let o=null==(a=globalThis[d])?void 0:a.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...o,[(0,r.normalizeAppPath)(e)]:t},serverActionsManifest:i,serverModuleMap:n}}function m(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function h(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,i=o.workAsyncStorage.getStore();if(!i){var n=t;let e=Object.values(n),i={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)i.clientModules={...i.clientModules,...t.clientModules},i.edgeRscModuleMapping={...i.edgeRscModuleMapping,...t.edgeRscModuleMapping},i.rscModuleMapping={...i.rscModuleMapping,...t.rscModuleMapping};return i}let r=t[i.route];if(!r)throw Object.defineProperty(new a.InvariantError(`Missing Client Reference Manifest for ${i.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return r}async function f(){if(n)return n;let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new a.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},48477:(e,t,i)=>{"use strict";let n=i(55511),a=i(29021),r=i(41957),o=i(27910).PassThrough,s=i(89581),l=i(34e3),c=i(59021),p=i(34295),d=i(91677),u=i(29564),m=i(5328),h=i(39358),f=i(31237);class g{constructor(e,t){this.nodeCounter=0,t=t||{},this.baseBoundary=t.baseBoundary||n.randomBytes(8).toString("hex"),this.boundaryPrefix=t.boundaryPrefix||"--_NmP",this.disableFileAccess=!!t.disableFileAccess,this.disableUrlAccess=!!t.disableUrlAccess,this.normalizeHeaderKey=t.normalizeHeaderKey,this.date=new Date,this.rootNode=t.rootNode||this,this.keepBcc=!!t.keepBcc,t.filename&&(this.filename=t.filename,e||(e=l.detectMimeType(this.filename.split(".").pop()))),this.textEncoding=(t.textEncoding||"").toString().trim().charAt(0).toUpperCase(),this.parentNode=t.parentNode,this.hostname=t.hostname,this.newline=t.newline,this.childNodes=[],this._nodeId=++this.rootNode.nodeCounter,this._headers=[],this._isPlainText=!1,this._hasLongLines=!1,this._envelope=!1,this._raw=!1,this._transforms=[],this._processFuncs=[],e&&this.setHeader("Content-Type",e)}createChild(e,t){t||"object"!=typeof e||(t=e,e=void 0);let i=new g(e,t);return this.appendChild(i),i}appendChild(e){return e.rootNode!==this.rootNode&&(e.rootNode=this.rootNode,e._nodeId=++this.rootNode.nodeCounter),e.parentNode=this,this.childNodes.push(e),e}replace(e){return e===this?this:(this.parentNode.childNodes.forEach((t,i)=>{t===this&&(e.rootNode=this.rootNode,e.parentNode=this.parentNode,e._nodeId=this._nodeId,this.rootNode=this,this.parentNode=void 0,e.parentNode.childNodes[i]=e)}),e)}remove(){if(!this.parentNode)return this;for(let e=this.parentNode.childNodes.length-1;e>=0;e--)if(this.parentNode.childNodes[e]===this)return this.parentNode.childNodes.splice(e,1),this.parentNode=void 0,this.rootNode=this,this}setHeader(e,t){let i=!1,n;if(!t&&e&&"object"==typeof e)return e.key&&"value"in e?this.setHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.setHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.setHeader(t,e[t])}),this;n={key:e=this._normalizeHeaderKey(e),value:t};for(let t=0,a=this._headers.length;t<a;t++)this._headers[t].key===e&&(i?(this._headers.splice(t,1),t--,a--):(this._headers[t]=n,i=!0));return i||this._headers.push(n),this}addHeader(e,t){return!t&&e&&"object"==typeof e?e.key&&e.value?this.addHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.addHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.addHeader(t,e[t])}):Array.isArray(t)?t.forEach(t=>{this.addHeader(e,t)}):this._headers.push({key:this._normalizeHeaderKey(e),value:t}),this}getHeader(e){e=this._normalizeHeaderKey(e);for(let t=0,i=this._headers.length;t<i;t++)if(this._headers[t].key===e)return this._headers[t].value}setContent(e){return this.content=e,"function"==typeof this.content.pipe?(this._contentErrorHandler=e=>{this.content.removeListener("error",this._contentErrorHandler),this.content=e},this.content.once("error",this._contentErrorHandler)):"string"==typeof this.content&&(this._isPlainText=l.isPlainText(this.content),this._isPlainText&&l.hasLongerLines(this.content,76)&&(this._hasLongLines=!0)),this}build(e){let t;e||(t=new Promise((t,i)=>{e=s.callbackPromise(t,i)}));let i=this.createReadStream(),n=[],a=0,r=!1;return i.on("readable",()=>{let e;for(;null!==(e=i.read());)n.push(e),a+=e.length}),i.once("error",t=>{if(!r)return r=!0,e(t)}),i.once("end",t=>{if(!r)return r=!0,t&&t.length&&(n.push(t),a+=t.length),e(null,Buffer.concat(n,a))}),t}getTransferEncoding(){let e=!1,t=(this.getHeader("Content-Type")||"").toString().toLowerCase().trim();return this.content&&((e=(this.getHeader("Content-Transfer-Encoding")||"").toString().toLowerCase().trim())&&["base64","quoted-printable"].includes(e)||(/^text\//i.test(t)?e=this._isPlainText&&!this._hasLongLines?"7bit":"string"==typeof this.content||this.content instanceof Buffer?"Q"===this._getTextEncoding(this.content)?"quoted-printable":"base64":"B"===this.textEncoding?"base64":"quoted-printable":/^(multipart|message)\//i.test(t)||(e=e||"base64"))),e}buildHeaders(){let e=this.getTransferEncoding(),t=[];if(e&&this.setHeader("Content-Transfer-Encoding",e),this.filename&&!this.getHeader("Content-Disposition")&&this.setHeader("Content-Disposition","attachment"),this.rootNode===this){this.getHeader("Date")||this.setHeader("Date",this.date.toUTCString().replace(/GMT/,"+0000")),this.messageId(),this.getHeader("MIME-Version")||this.setHeader("MIME-Version","1.0");for(let e=this._headers.length-2;e>=0;e--){let t=this._headers[e];"Content-Type"===t.key&&(this._headers.splice(e,1),this._headers.push(t))}}return this._headers.forEach(e=>{let i,n,a=e.key,r=e.value,o={};if(!r||"object"!=typeof r||["From","Sender","To","Cc","Bcc","Reply-To","Date","References"].includes(a)||(Object.keys(r).forEach(e=>{"value"!==e&&(o[e]=r[e])}),(r=(r.value||"").toString()).trim())){if(o.prepared)return void(o.foldLines?t.push(l.foldLines(a+": "+r)):t.push(a+": "+r));switch(e.key){case"Content-Disposition":i=l.parseHeaderValue(r),this.filename&&(i.params.filename=this.filename),r=l.buildHeaderValue(i);break;case"Content-Type":i=l.parseHeaderValue(r),this._handleContentType(i),i.value.match(/^text\/plain\b/)&&"string"==typeof this.content&&/[\u0080-\uFFFF]/.test(this.content)&&(i.params.charset="utf-8"),r=l.buildHeaderValue(i),this.filename&&(((n=this._encodeWords(this.filename))!==this.filename||/[\s'"\\;:/=(),<>@[\]?]|^-/.test(n))&&(n='"'+n+'"'),r+="; name="+n);break;case"Bcc":if(!this.keepBcc)return}if(((r=this._encodeHeaderValue(a,r))||"").toString().trim()){if("function"==typeof this.normalizeHeaderKey){let e=this.normalizeHeaderKey(a,r);e&&"string"==typeof e&&e.length&&(a=e)}t.push(l.foldLines(a+": "+r,76))}}}),t.join("\r\n")}createReadStream(e){let t,i=new o(e=e||{}),n=i;this.stream(i,e,e=>{if(e)return void n.emit("error",e);i.end()});for(let e=0,i=this._transforms.length;e<i;e++)t="function"==typeof this._transforms[e]?this._transforms[e]():this._transforms[e],n.once("error",e=>{t.emit("error",e)}),n=n.pipe(t);t=new m,n.once("error",e=>{t.emit("error",e)}),n=n.pipe(t);for(let e=0,i=this._processFuncs.length;e<i;e++)n=(t=this._processFuncs[e])(n);if(this.newline){let e=["win","windows","dos","\r\n"].includes(this.newline.toString().toLowerCase())?new h:new f,t=n.pipe(e);return n.on("error",e=>t.emit("error",e)),t}return n}transform(e){this._transforms.push(e)}processFunc(e){this._processFuncs.push(e)}stream(e,t,i){let n,a,r=this.getTransferEncoding(),o=!1,s=e=>{o||(o=!0,i(e))},l=()=>{let i=0,n=()=>{if(i>=this.childNodes.length)return e.write("\r\n--"+this.boundary+"--\r\n"),s();let a=this.childNodes[i++];e.write((i>1?"\r\n":"")+"--"+this.boundary+"\r\n"),a.stream(e,t,e=>{if(e)return s(e);setImmediate(n)})};if(!this.multipart)return s();setImmediate(n)};this._raw?setImmediate(()=>{if("[object Error]"===Object.prototype.toString.call(this._raw))return s(this._raw);"function"==typeof this._raw.pipe&&this._raw.removeListener("error",this._contentErrorHandler);let t=this._getStream(this._raw);t.pipe(e,{end:!1}),t.on("error",t=>e.emit("error",t)),t.on("end",l)}):(e.write(this.buildHeaders()+"\r\n\r\n"),setImmediate(()=>{if(!this.content)return setImmediate(l);{if("[object Error]"===Object.prototype.toString.call(this.content))return s(this.content);"function"==typeof this.content.pipe&&(this.content.removeListener("error",this._contentErrorHandler),this._contentErrorHandler=e=>s(e),this.content.once("error",this._contentErrorHandler));let i=()=>{["quoted-printable","base64"].includes(r)?((n=new("base64"===r?p:c).Encoder(t)).pipe(e,{end:!1}),n.once("end",l),n.once("error",e=>s(e)),(a=this._getStream(this.content)).pipe(n)):((a=this._getStream(this.content)).pipe(e,{end:!1}),a.once("end",l)),a.once("error",e=>s(e))};if(this.content._resolve){let e=[],t=0,n=!1,a=this._getStream(this.content);a.on("error",e=>{n||(n=!0,s(e))}),a.on("readable",()=>{let i;for(;null!==(i=a.read());)e.push(i),t+=i.length}),a.on("end",()=>{n||(n=!0,this.content._resolve=!1,this.content._resolvedValue=Buffer.concat(e,t),setImmediate(i))})}else setImmediate(i);return}}))}setEnvelope(e){let t;this._envelope={from:!1,to:[]},e.from&&(t=[],this._convertAddresses(this._parseAddresses(e.from),t),(t=t.filter(e=>e&&e.address)).length&&t[0]&&(this._envelope.from=t[0].address)),["to","cc","bcc"].forEach(t=>{e[t]&&this._convertAddresses(this._parseAddresses(e[t]),this._envelope.to)}),this._envelope.to=this._envelope.to.map(e=>e.address).filter(e=>e);let i=["to","cc","bcc","from"];return Object.keys(e).forEach(t=>{i.includes(t)||(this._envelope[t]=e[t])}),this}getAddresses(){let e={};return this._headers.forEach(t=>{let i=t.key.toLowerCase();["from","sender","reply-to","to","cc","bcc"].includes(i)&&(Array.isArray(e[i])||(e[i]=[]),this._convertAddresses(this._parseAddresses(t.value),e[i]))}),e}getEnvelope(){if(this._envelope)return this._envelope;let e={from:!1,to:[]};return this._headers.forEach(t=>{let i=[];"From"===t.key||!e.from&&["Reply-To","Sender"].includes(t.key)?(this._convertAddresses(this._parseAddresses(t.value),i),i.length&&i[0]&&(e.from=i[0].address)):["To","Cc","Bcc"].includes(t.key)&&this._convertAddresses(this._parseAddresses(t.value),e.to)}),e.to=e.to.map(e=>e.address),e}messageId(){let e=this.getHeader("Message-ID");return e||(e=this._generateMessageId(),this.setHeader("Message-ID",e)),e}setRaw(e){return this._raw=e,this._raw&&"function"==typeof this._raw.pipe&&(this._contentErrorHandler=e=>{this._raw.removeListener("error",this._contentErrorHandler),this._raw=e},this._raw.once("error",this._contentErrorHandler)),this}_getStream(e){let t;return e._resolvedValue?(t=new o,setImmediate(()=>{try{t.end(e._resolvedValue)}catch(e){t.emit("error",e)}}),t):"function"==typeof e.pipe?e:e&&"string"==typeof e.path&&!e.href?this.disableFileAccess?(t=new o,setImmediate(()=>t.emit("error",Error("File access rejected for "+e.path))),t):a.createReadStream(e.path):e&&"string"==typeof e.href?this.disableUrlAccess?(t=new o,setImmediate(()=>t.emit("error",Error("Url access rejected for "+e.href))),t):u(e.href,{headers:e.httpHeaders}):(t=new o,setImmediate(()=>{try{t.end(e||"")}catch(e){t.emit("error",e)}}),t)}_parseAddresses(e){return[].concat.apply([],[].concat(e).map(e=>e&&e.address?(e.address=this._normalizeAddress(e.address),e.name=e.name||"",[e]):d(e)))}_normalizeHeaderKey(e){return e=(e||"").toString().replace(/\r?\n|\r/g," ").trim().toLowerCase().replace(/^X-SMTPAPI$|^(MIME|DKIM|ARC|BIMI)\b|^[a-z]|-(SPF|FBL|ID|MD5)$|-[a-z]/gi,e=>e.toUpperCase()).replace(/^Content-Features$/i,"Content-features")}_handleContentType(e){this.contentType=e.value.trim().toLowerCase(),this.multipart=!!/^multipart\//i.test(this.contentType)&&this.contentType.substr(this.contentType.indexOf("/")+1),this.multipart?this.boundary=e.params.boundary=e.params.boundary||this.boundary||this._generateBoundary():this.boundary=!1}_generateBoundary(){return this.rootNode.boundaryPrefix+"-"+this.rootNode.baseBoundary+"-Part_"+this._nodeId}_encodeHeaderValue(e,t){switch(e=this._normalizeHeaderKey(e)){case"From":case"Sender":case"To":case"Cc":case"Bcc":case"Reply-To":return this._convertAddresses(this._parseAddresses(t));case"Message-ID":case"In-Reply-To":case"Content-Id":return"<"!==(t=(t||"").toString().replace(/\r?\n|\r/g," ")).charAt(0)&&(t="<"+t),">"!==t.charAt(t.length-1)&&(t+=">"),t;case"References":return(t=[].concat.apply([],[].concat(t||"").map(e=>(e=(e||"").toString().replace(/\r?\n|\r/g," ").trim()).replace(/<[^>]*>/g,e=>e.replace(/\s/g,"")).split(/\s+/))).map(e=>("<"!==e.charAt(0)&&(e="<"+e),">"!==e.charAt(e.length-1)&&(e+=">"),e))).join(" ").trim();case"Date":if("[object Date]"===Object.prototype.toString.call(t))return t.toUTCString().replace(/GMT/,"+0000");return t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t);case"Content-Type":case"Content-Disposition":return(t||"").toString().replace(/\r?\n|\r/g," ");default:return t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t)}}_convertAddresses(e,t){let i=[];return t=t||[],[].concat(e||[]).forEach(e=>{if(e.address)e.address=this._normalizeAddress(e.address),e.name?e.name&&i.push(`${this._encodeAddressName(e.name)} <${e.address}>`):i.push(e.address.indexOf(" ")>=0?`<${e.address}>`:`${e.address}`),e.address&&!t.filter(t=>t.address===e.address).length&&t.push(e);else if(e.group){let n=(e.group.length?this._convertAddresses(e.group,t):"").trim();i.push(`${this._encodeAddressName(e.name)}:${n};`)}}),i.join(", ")}_normalizeAddress(e){let t,i=(e=(e||"").toString().replace(/[\x00-\x1F<>]+/g," ").trim()).lastIndexOf("@");if(i<0)return e;let n=e.substr(0,i),a=e.substr(i+1);try{t=r.toASCII(a.toLowerCase())}catch(e){}return n.indexOf(" ")>=0&&('"'!==n.charAt(0)&&(n='"'+n),'"'!==n.substr(-1)&&(n+='"')),`${n}@${t}`}_encodeAddressName(e){if(!/^[\w ]*$/.test(e))if(/^[\x20-\x7e]*$/.test(e))return'"'+e.replace(/([\\"])/g,"\\$1")+'"';else return l.encodeWord(e,this._getTextEncoding(e),52);return e}_encodeWords(e){return l.encodeWords(e,this._getTextEncoding(e),52,!0)}_getTextEncoding(e){e=(e||"").toString();let t=this.textEncoding;return t||(t=(e.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\u0080-\uFFFF]/g)||[]).length<(e.match(/[a-z]/gi)||[]).length?"Q":"B"),t}_generateMessageId(){return"<"+[2,2,2,6].reduce((e,t)=>e+"-"+n.randomBytes(t).toString("hex"),n.randomBytes(4).toString("hex"))+"@"+(this.getEnvelope().from||this.hostname||"localhost").split("@").pop()+">"}}e.exports=g},53312:(e,t,i)=>{"use strict";let n=i(91423),a=i(89581);class r{constructor(e){e=e||{},this.options=e||{},this.name="JSONTransport",this.version=n.version,this.logger=a.getLogger(this.options,{component:this.options.component||"json-transport"})}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),n=e.message.messageId(),a=[].concat(i.to||[]);a.length>3&&a.push("...and "+a.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:n},"Composing JSON structure of %s to <%s>",n,a.join(", ")),setImmediate(()=>{e.normalize((e,a)=>e?(this.logger.error({err:e,tnx:"send",messageId:n},"Failed building JSON structure for %s. %s",n,e.message),t(e)):(delete a.envelope,delete a.normalizedHeaders,t(null,{envelope:i,messageId:n,message:this.options.skipEncoding?a:JSON.stringify(a)})))})}}e.exports=r},54387:()=>{},54806:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"MISSING_ROOT_TAGS_ERROR",{enumerable:!0,get:function(){return i}});let i="NEXT_MISSING_ROOT_TAGS";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58812:(e,t,i)=>{"use strict";let n=i(3350),a={};function r(e){return e.replace(/[^a-zA-Z0-9.-]/g,"").toLowerCase()}function o(e){let t=["domains","aliases"],i={};return Object.keys(e).forEach(n=>{0>t.indexOf(n)&&(i[n]=e[n])}),i}Object.keys(n).forEach(e=>{let t=n[e];a[r(e)]=o(t),[].concat(t.aliases||[]).forEach(e=>{a[r(e)]=o(t)}),[].concat(t.domains||[]).forEach(e=>{a[r(e)]=o(t)})}),e.exports=function(e){return a[e=r(e.split("@").pop())]||!1}},59021:(e,t,i)=>{"use strict";let n=i(27910).Transform;function a(e){let t;"string"==typeof e&&(e=Buffer.from(e,"utf-8"));let i=[[9],[10],[13],[32,60],[62,126]],n="";for(let a=0,r=e.length;a<r;a++){if(function(e,t){for(let i=t.length-1;i>=0;i--)if(t[i].length&&(1===t[i].length&&e===t[i][0]||2===t[i].length&&e>=t[i][0]&&e<=t[i][1]))return!0;return!1}(t=e[a],i)&&(32!==t&&9!==t||a!==r-1&&10!==e[a+1]&&13!==e[a+1])){n+=String.fromCharCode(t);continue}n+="="+(t<16?"0":"")+t.toString(16).toUpperCase()}return n}function r(e,t){let i,n,a;if(e=(e||"").toString(),t=t||76,e.length<=t)return e;let r=0,o=e.length,s=Math.floor(t/3),l="";for(;r<o;){if(i=(a=e.substr(r,t)).match(/\r\n/)){l+=a=a.substr(0,i.index+i[0].length),r+=a.length;continue}if("\n"===a.substr(-1)){l+=a,r+=a.length;continue}if(i=a.substr(-s).match(/\n.*?$/)){l+=a=a.substr(0,a.length-(i[0].length-1)),r+=a.length;continue}if(a.length>t-s&&(i=a.substr(-s).match(/[ \t.,!?][^ \t.,!?]*$/)))a=a.substr(0,a.length-(i[0].length-1));else if(a.match(/[=][\da-f]{0,2}$/i))for((i=a.match(/[=][\da-f]{0,1}$/i))&&(a=a.substr(0,a.length-i[0].length));a.length>3&&a.length<o-r&&!a.match(/^(?:=[\da-f]{2}){1,4}$/i)&&(i=a.match(/[=][\da-f]{2}$/gi))&&!((n=parseInt(i[0].substr(1,2),16))<128)&&(a=a.substr(0,a.length-3),!(n>=192)););r+a.length<o&&"\n"!==a.substr(-1)?(a.length===t&&a.match(/[=][\da-f]{2}$/i)?a=a.substr(0,a.length-3):a.length===t&&(a=a.substr(0,a.length-1)),r+=a.length,a+="=\r\n"):r+=a.length,l+=a}return l}class o extends n{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){let n;if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return i();this.inputBytes+=e.length,this.options.lineLength?(n=(n=r(n=this._curLine+a(e),this.options.lineLength)).replace(/(^|\n)([^\n]*)$/,(e,t,i)=>(this._curLine=i,t)))&&(this.outputBytes+=n.length,this.push(n)):(n=a(e),this.outputBytes+=n.length,this.push(n,"ascii")),i()}_flush(e){this._curLine&&(this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii")),e()}}e.exports={encode:a,wrap:r,Encoder:o}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64390:(e,t,i)=>{"use strict";e.exports=i(34821)},66224:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return a}});let i="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=i}}function a(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===i}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66647:(e,t,i)=>{"use strict";let n=i(94735),a=i(91423),r=i(89581),o=i(39358),s=i(48477);class l extends n{constructor(e){super(),e=e||{},this.options=e||{},this.ses=this.options.SES,this.name="SESTransport",this.version=a.version,this.logger=r.getLogger(this.options,{component:this.options.component||"ses-transport"})}getRegion(e){return this.ses.sesClient.config&&"function"==typeof this.ses.sesClient.config.region?this.ses.sesClient.config.region().then(t=>e(null,t)).catch(t=>e(t)):e(null,!1)}send(e,t){let i={ts:Date.now(),pending:!0},n=e.message._headers.find(e=>/^from$/i.test(e.key));if(n){let e=new s("text/plain");n=e._convertAddresses(e._parseAddresses(n.value))}let a=e.data.envelope||e.message.getEnvelope(),r=e.message.messageId(),l=[].concat(a.to||[]);l.length>3&&l.push("...and "+l.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:r},"Sending message %s to <%s>",r,l.join(", "));let c=t=>{e.data._dkim||(e.data._dkim={}),e.data._dkim.skipFields&&"string"==typeof e.data._dkim.skipFields?e.data._dkim.skipFields+=":date:message-id":e.data._dkim.skipFields="date:message-id";let i=e.message.createReadStream(),n=i.pipe(new o),a=[],r=0;n.on("readable",()=>{let e;for(;null!==(e=n.read());)a.push(e),r+=e.length}),i.once("error",e=>n.emit("error",e)),n.once("error",e=>{t(e)}),n.once("end",()=>t(null,Buffer.concat(a,r)))};setImmediate(()=>c((o,s)=>{if(o)return this.logger.error({err:o,tnx:"send",messageId:r},"Failed creating message for %s. %s",r,o.message),i.pending=!1,t(o);let l={Content:{Raw:{Data:s}},FromEmailAddress:n||a.from,Destination:{ToAddresses:a.to}};Object.keys(e.data.ses||{}).forEach(t=>{l[t]=e.data.ses[t]}),this.getRegion((e,n)=>{(e||!n)&&(n="us-east-1");let o=new this.ses.SendEmailCommand(l);this.ses.sesClient.send(o).then(e=>{"us-east-1"===n&&(n="email"),i.pending=!0,t(null,{envelope:{from:a.from,to:a.to},messageId:"<"+e.MessageId+(/@/.test(e.MessageId)?"":"@"+n+".amazonses.com")+">",response:e.MessageId,raw:s})}).catch(e=>{this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",r,e.message),i.pending=!1,t(e)})})}))}verify(e){let t;e||(t=new Promise((t,i)=>{e=r.callbackPromise(t,i)}));let i=t=>t&&!["InvalidParameterValue","MessageRejected"].includes(t.code||t.Code||t.name)?e(t):e(null,!0),n={Content:{Raw:{Data:Buffer.from("From: <invalid@invalid>\r\nTo: <invalid@invalid>\r\n Subject: Invalid\r\n\r\nInvalid")}},FromEmailAddress:"invalid@invalid",Destination:{ToAddresses:["invalid@invalid"]}};return this.getRegion((e,t)=>{(e||!t)&&(t="us-east-1");let a=new this.ses.SendEmailCommand(n);this.ses.sesClient.send(a).then(e=>i(null,e)).catch(e=>i(e))}),t}}e.exports=l},68790:(e,t,i)=>{"use strict";e.exports=i(65239).vendored["react-rsc"].ReactDOM},70830:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{METADATA_BOUNDARY_NAME:function(){return i},OUTLET_BOUNDARY_NAME:function(){return a},VIEWPORT_BOUNDARY_NAME:function(){return n}});let i="__next_metadata_boundary__",n="__next_viewport_boundary__",a="__next_outlet_boundary__"},73214:e=>{(()=>{"use strict";var t={491:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=i(223),a=i(172),r=i(930),o="context",s=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,r.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,i,...n){return this._getContextManager().with(e,t,i,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,r.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=i(56),a=i(912),r=i(957),o=i(172);class s{constructor(){function e(e){return function(...t){let i=(0,o.getGlobal)("diag");if(i)return i[e](...t)}}let t=this;t.setLogger=(e,i={logLevel:r.DiagLogLevel.INFO})=>{var n,s,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof i&&(i={logLevel:i});let c=(0,o.getGlobal)("diag"),p=(0,a.createLogLevelDiagLogger)(null!=(s=i.logLevel)?s:r.DiagLogLevel.INFO,e);if(c&&!i.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),p.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",p,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=i(660),a=i(172),r=i(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,r.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,i){return this.getMeterProvider().getMeter(e,t,i)}disable(){(0,a.unregisterGlobal)(o,r.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=i(172),a=i(874),r=i(194),o=i(277),s=i(369),l=i(930),c="propagation",p=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,i=r.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,i)}extract(e,t,i=r.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,i)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(c)||p}}t.PropagationAPI=d},997:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=i(172),a=i(846),r=i(139),o=i(607),s=i(930),l="trace";class c{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=r.wrapSpanContext,this.isSpanContextValid=r.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=i(491),a=(0,i(780).createContextKey)("OpenTelemetry Baggage Key");function r(e){return e.getValue(a)||void 0}t.getBaggage=r,t.getActiveBaggage=function(){return r(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class i{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new i(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new i(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new i(this._entries);for(let i of e)t._entries.delete(i);return t}clear(){return new i}}t.BaggageImpl=i},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=i(930),a=i(993),r=i(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:r.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=i(491).ContextAPI.getInstance()},223:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=i(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,i,...n){return t.call(i,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class i{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new i(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new i(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new i},506:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=i(930).DiagAPI.instance()},56:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=i(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return r("debug",this._namespace,e)}error(...e){return r("error",this._namespace,e)}info(...e){return r("info",this._namespace,e)}warn(...e){return r("warn",this._namespace,e)}verbose(...e){return r("verbose",this._namespace,e)}}function r(e,t,i){let a=(0,n.getGlobal)("diag");if(a)return i.unshift(t),a[e](...i)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let i=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<i.length;e++)this[i[e].n]=function(e){return function(...t){if(console){let i=console[e];if("function"!=typeof i&&(i=console.log),"function"==typeof i)return i.apply(console,t)}}}(i[e].c)}}t.DiagConsoleLogger=n},912:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=i(957);t.createLogLevelDiagLogger=function(e,t){function i(i,n){let a=t[i];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:i("error",n.DiagLogLevel.ERROR),warn:i("warn",n.DiagLogLevel.WARN),info:i("info",n.DiagLogLevel.INFO),debug:i("debug",n.DiagLogLevel.DEBUG),verbose:i("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=i(200),a=i(521),r=i(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),l=n._globalThis;t.registerGlobal=function(e,t,i,n=!1){var r;let o=l[s]=null!=(r=l[s])?r:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return i.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return i.error(t.stack||t.message),!1}return o[e]=t,i.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,i;let n=null==(t=l[s])?void 0:t.version;if(n&&(0,r.isCompatible)(n))return null==(i=l[s])?void 0:i[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let i=l[s];i&&delete i[e]}},130:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=i(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function r(e){let t=new Set([e]),i=new Set,n=e.match(a);if(!n)return()=>!1;let r={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=r.prerelease)return function(t){return t===e};function o(e){return i.add(e),!1}return function(e){if(t.has(e))return!0;if(i.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||r.major!==s.major)return o(e);if(0===r.major)return r.minor===s.minor&&r.patch<=s.patch?(t.add(e),!0):o(e);return r.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=r,t.isCompatible=r(n.VERSION)},886:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=i(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class i{constructor(){}createHistogram(e,i){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,i){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,i){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,i){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,i){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,i){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=i;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class r extends n{add(e,t){}}t.NoopUpDownCounterMetric=r;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class l extends s{}t.NoopObservableCounterMetric=l;class c extends s{}t.NoopObservableGaugeMetric=c;class p extends s{}t.NoopObservableUpDownCounterMetric=p,t.NOOP_METER=new i,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new r,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new p,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=i(102);class a{getMeter(e,t,i){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[i]}})}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),a=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),a(i(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:global},46:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[i]}})}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),a=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),a(i(651),t)},939:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=i(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class i{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=i},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,i){null!=e&&(e[t]=i)}}},845:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=i(997).TraceAPI.getInstance()},403:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=i(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=i(491),a=i(607),r=i(403),o=i(139),s=n.ContextAPI.getInstance();class l{startSpan(e,t,i=s.active()){var n;if(null==t?void 0:t.root)return new r.NonRecordingSpan;let l=i&&(0,a.getSpanContext)(i);return"object"==typeof(n=l)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(l)?new r.NonRecordingSpan(l):new r.NonRecordingSpan}startActiveSpan(e,t,i,n){let r,o,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(r=t,l=i):(r=t,o=i,l=n);let c=null!=o?o:s.active(),p=this.startSpan(e,r,c),d=(0,a.setSpan)(c,p);return s.with(d,l,void 0,p)}}t.NoopTracer=l},124:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=i(614);class a{getTracer(e,t,i){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(i(614)).NoopTracer;class a{constructor(e,t,i,n){this._provider=e,this.name=t,this.version=i,this.options=n}startSpan(e,t,i){return this._getTracer().startSpan(e,t,i)}startActiveSpan(e,t,i,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=i(125),a=new(i(124)).NoopTracerProvider;class r{getTracer(e,t,i){var a;return null!=(a=this.getDelegateTracer(e,t,i))?a:new n.ProxyTracer(this,e,t,i)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,i){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,i)}}t.ProxyTracerProvider=r},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=i(780),a=i(403),r=i(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function l(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(r.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return l(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=i(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let i=this._clone();return i._internalState.has(e)&&i._internalState.delete(e),i._internalState.set(e,t),i}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let i=t.trim(),a=i.indexOf("=");if(-1!==a){let r=i.slice(0,a),o=i.slice(a+1,t.length);(0,n.validateKey)(r)&&(0,n.validateValue)(o)&&e.set(r,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let i="[_0-9a-z-*/]",n=`[a-z]${i}{0,255}`,a=`[a-z0-9]${i}{0,240}@[a-z]${i}{0,13}`,r=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return r.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=i(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=i(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=i(476),a=i(403),r=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return r.test(e)&&e!==n.INVALID_TRACEID}function l(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=l,t.isSpanContextValid=function(e){return s(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function n(e){var a=i[e];if(void 0!==a)return a.exports;var r=i[e]={exports:{}},o=!0;try{t[e].call(r.exports,r,r.exports,n),o=!1}finally{o&&delete i[e]}return r.exports}n.ab=__dirname+"/";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var i=n(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return i.DiagConsoleLogger}});var r=n(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return r.DiagLogLevel}});var o=n(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=n(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var l=n(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=n(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var p=n(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return p.ProxyTracerProvider}});var d=n(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var u=n(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return u.SpanKind}});var m=n(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return m.SpanStatusCode}});var h=n(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return h.TraceFlags}});var f=n(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return f.createTraceState}});var g=n(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return g.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return g.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return g.isValidSpanId}});var v=n(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return v.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return v.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return v.INVALID_SPAN_CONTEXT}});let x=n(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return x.context}});let b=n(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return b.diag}});let y=n(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return y.metrics}});let _=n(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return _.propagation}});let w=n(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return w.trace}}),a.default={context:x.context,diag:b.diag,metrics:y.metrics,propagation:_.propagation,trace:w.trace}})(),e.exports=a})()},74075:e=>{"use strict";e.exports=require("zlib")},74932:(e,t,i)=>{"use strict";e.exports=i(65239).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},75113:(e,t,i)=>{"use strict";let n=i(79551);class a{constructor(e){this.options=e||{},this.cookies=[]}set(e,t){let i,a=n.parse(t||""),r=this.parse(e);return r.domain?(i=r.domain.replace(/^\./,""),(a.hostname.length<i.length||("."+a.hostname).substr(-i.length+1)!=="."+i)&&(r.domain=a.hostname)):r.domain=a.hostname,r.path||(r.path=this.getPath(a.pathname)),r.expires||(r.expires=new Date(Date.now()+1e3*(Number(this.options.sessionTimeout||1800)||1800))),this.add(r)}get(e){return this.list(e).map(e=>e.name+"="+e.value).join("; ")}list(e){let t,i,n=[];for(t=this.cookies.length-1;t>=0;t--){if(i=this.cookies[t],this.isExpired(i)){this.cookies.splice(t,t);continue}this.match(i,e)&&n.unshift(i)}return n}parse(e){let t={};return(e||"").toString().split(";").forEach(e=>{let i,n=e.split("="),a=n.shift().trim().toLowerCase(),r=n.join("=").trim();if(a)switch(a){case"expires":"Invalid Date"!==(r=new Date(r)).toString()&&(t.expires=r);break;case"path":t.path=r;break;case"domain":(i=r.toLowerCase()).length&&"."!==i.charAt(0)&&(i="."+i),t.domain=i;break;case"max-age":t.expires=new Date(Date.now()+1e3*(Number(r)||0));break;case"secure":t.secure=!0;break;case"httponly":t.httponly=!0;break;default:t.name||(t.name=a,t.value=r)}}),t}match(e,t){let i=n.parse(t||"");return(i.hostname===e.domain||"."===e.domain.charAt(0)&&("."+i.hostname).substr(-e.domain.length)===e.domain)&&this.getPath(i.pathname).substr(0,e.path.length)===e.path&&(!e.secure||"https:"===i.protocol)}add(e){let t,i;if(!e||!e.name)return!1;for(t=0,i=this.cookies.length;t<i;t++)if(this.compare(this.cookies[t],e)){if(this.isExpired(e))return this.cookies.splice(t,1),!1;return this.cookies[t]=e,!0}return this.isExpired(e)||this.cookies.push(e),!0}compare(e,t){return e.name===t.name&&e.path===t.path&&e.domain===t.domain&&e.secure===t.secure&&e.httponly==e.httponly}isExpired(e){return e.expires&&e.expires<new Date||!e.value}getPath(e){let t=(e||"/").split("/");return t.pop(),"/"!==(t=t.join("/").trim()).charAt(0)&&(t="/"+t),"/"!==t.substr(-1)&&(t+="/"),t}}e.exports=a},75124:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{Postpone:function(){return k},abortAndThrowOnSynchronousRequestDataAccess:function(){return w},abortOnSynchronousPlatformIOAccess:function(){return y},accessedDynamicData:function(){return R},annotateDynamicAccess:function(){return B},consumeDynamicAccess:function(){return P},createDynamicTrackingState:function(){return u},createDynamicValidationState:function(){return m},createHangingInputAbortSignal:function(){return D},createPostponedAbortSignal:function(){return L},formatDynamicAPIAccesses:function(){return M},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return A},isPrerenderInterruptedError:function(){return j},markCurrentScopeAsDynamic:function(){return f},postponeWithTracking:function(){return S},throwIfDisallowedDynamic:function(){return G},throwToInterruptStaticGeneration:function(){return v},trackAllowedDynamicAccess:function(){return $},trackDynamicDataInDynamicRender:function(){return x},trackFallbackParamAccessed:function(){return g},trackSynchronousPlatformIOAccessInDev:function(){return _},trackSynchronousRequestDataAccessInDev:function(){return E},useDynamicRouteParams:function(){return H}});let n=function(e){return e&&e.__esModule?e:{default:e}}(i(7153)),a=i(38248),r=i(66224),o=i(63033),s=i(29294),l=i(37461),c=i(70830),p=i(97748),d="function"==typeof n.default.unstable_postpone;function u(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function m(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function f(e,t,i){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new r.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${i}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)S(e.route,i,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new a.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${i}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=i,e.dynamicUsageStack=n.stack,n}}}}function g(e,t){let i=o.workUnitAsyncStorage.getStore();i&&"prerender-ppr"===i.type&&S(e.route,t,i.dynamicTracking)}function v(e,t,i){let n=Object.defineProperty(new a.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw i.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function x(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function b(e,t,i){let n=N(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);i.controller.abort(n);let a=i.dynamicTracking;a&&a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function y(e,t,i,n){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=i),b(e,t,n)}function _(e){e.prerenderPhase=!1}function w(e,t,i,n){if(!1===n.controller.signal.aborted){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=i,!0===n.validating&&(a.syncDynamicLogged=!0)),b(e,t,n)}throw N(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}let E=_;function k({reason:e,route:t}){let i=o.workUnitAsyncStorage.getStore();S(t,e,i&&"prerender-ppr"===i.type?i.dynamicTracking:null)}function S(e,t,i){I(),i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(T(e,t))}function T(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function A(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&O(e.message)}function O(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===O(T("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let C="NEXT_PRERENDER_INTERRUPTED";function N(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=C,t}function j(e){return"object"==typeof e&&null!==e&&e.digest===C&&"name"in e&&"message"in e&&e instanceof Error}function R(e){return e.length>0}function P(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function M(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function I(){if(!d)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function L(e){I();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}function D(e){let t=new AbortController;return e.cacheSignal?e.cacheSignal.inputReady().then(()=>{t.abort()}):(0,p.scheduleOnNextTick)(()=>t.abort()),t.signal}function B(e,t){let i=t.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function H(e){let t=s.workAsyncStorage.getStore();if(t&&t.isStaticGeneration&&t.fallbackRouteParams&&t.fallbackRouteParams.size>0){let i=o.workUnitAsyncStorage.getStore();i&&("prerender"===i.type?n.default.use((0,l.makeHangingPromise)(i.renderSignal,e)):"prerender-ppr"===i.type?S(t.route,e,i.dynamicTracking):"prerender-legacy"===i.type&&v(e,t,i))}}let U=/\n\s+at Suspense \(<anonymous>\)/,q=RegExp(`\\n\\s+at ${c.METADATA_BOUNDARY_NAME}[\\n\\s]`),z=RegExp(`\\n\\s+at ${c.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),F=RegExp(`\\n\\s+at ${c.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function $(e,t,i,n,a){if(!F.test(t)){if(q.test(t)){i.hasDynamicMetadata=!0;return}if(z.test(t)){i.hasDynamicViewport=!0;return}if(U.test(t)){i.hasSuspendedDynamic=!0;return}else if(n.syncDynamicErrorWithStack||a.syncDynamicErrorWithStack){i.hasSyncDynamicErrors=!0;return}else{let n=function(e,t){let i=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.stack="Error: "+e+t,i}(`Route "${e}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);i.dynamicErrors.push(n);return}}}function G(e,t,i,n){let a,o,s;if(i.syncDynamicErrorWithStack?(a=i.syncDynamicErrorWithStack,o=i.syncDynamicExpression,s=!0===i.syncDynamicLogged):n.syncDynamicErrorWithStack?(a=n.syncDynamicErrorWithStack,o=n.syncDynamicExpression,s=!0===n.syncDynamicLogged):(a=null,o=void 0,s=!1),t.hasSyncDynamicErrors&&a)throw s||console.error(a),new r.StaticGenBailoutError;let l=t.dynamicErrors;if(l.length){for(let e=0;e<l.length;e++)console.error(l[e]);throw new r.StaticGenBailoutError}if(!t.hasSuspendedDynamic){if(t.hasDynamicMetadata){if(a)throw console.error(a),Object.defineProperty(new r.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new r.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(t.hasDynamicViewport){if(a)throw console.error(a),Object.defineProperty(new r.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new r.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}},77400:(e,t,i)=>{"use strict";let n=i(33873),a="application/octet-stream",r=new Map([["application/acad","dwg"],["application/applixware","aw"],["application/arj","arj"],["application/atom+xml","xml"],["application/atomcat+xml","atomcat"],["application/atomsvc+xml","atomsvc"],["application/base64",["mm","mme"]],["application/binhex","hqx"],["application/binhex4","hqx"],["application/book",["book","boo"]],["application/ccxml+xml,","ccxml"],["application/cdf","cdf"],["application/cdmi-capability","cdmia"],["application/cdmi-container","cdmic"],["application/cdmi-domain","cdmid"],["application/cdmi-object","cdmio"],["application/cdmi-queue","cdmiq"],["application/clariscad","ccad"],["application/commonground","dp"],["application/cu-seeme","cu"],["application/davmount+xml","davmount"],["application/drafting","drw"],["application/dsptype","tsp"],["application/dssc+der","dssc"],["application/dssc+xml","xdssc"],["application/dxf","dxf"],["application/ecmascript",["js","es"]],["application/emma+xml","emma"],["application/envoy","evy"],["application/epub+zip","epub"],["application/excel",["xls","xl","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/exi","exi"],["application/font-tdpfr","pfr"],["application/fractals","fif"],["application/freeloader","frl"],["application/futuresplash","spl"],["application/geo+json","geojson"],["application/gnutar","tgz"],["application/groupwise","vew"],["application/hlp","hlp"],["application/hta","hta"],["application/hyperstudio","stk"],["application/i-deas","unv"],["application/iges",["iges","igs"]],["application/inf","inf"],["application/internet-property-stream","acx"],["application/ipfix","ipfix"],["application/java","class"],["application/java-archive","jar"],["application/java-byte-code","class"],["application/java-serialized-object","ser"],["application/java-vm","class"],["application/javascript","js"],["application/json","json"],["application/lha","lha"],["application/lzx","lzx"],["application/mac-binary","bin"],["application/mac-binhex","hqx"],["application/mac-binhex40","hqx"],["application/mac-compactpro","cpt"],["application/macbinary","bin"],["application/mads+xml","mads"],["application/marc","mrc"],["application/marcxml+xml","mrcx"],["application/mathematica","ma"],["application/mathml+xml","mathml"],["application/mbedlet","mbd"],["application/mbox","mbox"],["application/mcad","mcd"],["application/mediaservercontrol+xml","mscml"],["application/metalink4+xml","meta4"],["application/mets+xml","mets"],["application/mime","aps"],["application/mods+xml","mods"],["application/mp21","m21"],["application/mp4","mp4"],["application/mspowerpoint",["ppt","pot","pps","ppz"]],["application/msword",["doc","dot","w6w","wiz","word"]],["application/mswrite","wri"],["application/mxf","mxf"],["application/netmc","mcp"],["application/octet-stream",["*"]],["application/oda","oda"],["application/oebps-package+xml","opf"],["application/ogg","ogx"],["application/olescript","axs"],["application/onenote","onetoc"],["application/patch-ops-error+xml","xer"],["application/pdf","pdf"],["application/pgp-encrypted","asc"],["application/pgp-signature","pgp"],["application/pics-rules","prf"],["application/pkcs-12","p12"],["application/pkcs-crl","crl"],["application/pkcs10","p10"],["application/pkcs7-mime",["p7c","p7m"]],["application/pkcs7-signature","p7s"],["application/pkcs8","p8"],["application/pkix-attr-cert","ac"],["application/pkix-cert",["cer","crt"]],["application/pkix-crl","crl"],["application/pkix-pkipath","pkipath"],["application/pkixcmp","pki"],["application/plain","text"],["application/pls+xml","pls"],["application/postscript",["ps","ai","eps"]],["application/powerpoint","ppt"],["application/pro_eng",["part","prt"]],["application/prs.cww","cww"],["application/pskc+xml","pskcxml"],["application/rdf+xml","rdf"],["application/reginfo+xml","rif"],["application/relax-ng-compact-syntax","rnc"],["application/resource-lists+xml","rl"],["application/resource-lists-diff+xml","rld"],["application/ringing-tones","rng"],["application/rls-services+xml","rs"],["application/rsd+xml","rsd"],["application/rss+xml","xml"],["application/rtf",["rtf","rtx"]],["application/sbml+xml","sbml"],["application/scvp-cv-request","scq"],["application/scvp-cv-response","scs"],["application/scvp-vp-request","spq"],["application/scvp-vp-response","spp"],["application/sdp","sdp"],["application/sea","sea"],["application/set","set"],["application/set-payment-initiation","setpay"],["application/set-registration-initiation","setreg"],["application/shf+xml","shf"],["application/sla","stl"],["application/smil",["smi","smil"]],["application/smil+xml","smi"],["application/solids","sol"],["application/sounder","sdr"],["application/sparql-query","rq"],["application/sparql-results+xml","srx"],["application/srgs","gram"],["application/srgs+xml","grxml"],["application/sru+xml","sru"],["application/ssml+xml","ssml"],["application/step",["step","stp"]],["application/streamingmedia","ssm"],["application/tei+xml","tei"],["application/thraud+xml","tfi"],["application/timestamped-data","tsd"],["application/toolbook","tbk"],["application/vda","vda"],["application/vnd.3gpp.pic-bw-large","plb"],["application/vnd.3gpp.pic-bw-small","psb"],["application/vnd.3gpp.pic-bw-var","pvb"],["application/vnd.3gpp2.tcap","tcap"],["application/vnd.3m.post-it-notes","pwn"],["application/vnd.accpac.simply.aso","aso"],["application/vnd.accpac.simply.imp","imp"],["application/vnd.acucobol","acu"],["application/vnd.acucorp","atc"],["application/vnd.adobe.air-application-installer-package+zip","air"],["application/vnd.adobe.fxp","fxp"],["application/vnd.adobe.xdp+xml","xdp"],["application/vnd.adobe.xfdf","xfdf"],["application/vnd.ahead.space","ahead"],["application/vnd.airzip.filesecure.azf","azf"],["application/vnd.airzip.filesecure.azs","azs"],["application/vnd.amazon.ebook","azw"],["application/vnd.americandynamics.acc","acc"],["application/vnd.amiga.ami","ami"],["application/vnd.android.package-archive","apk"],["application/vnd.anser-web-certificate-issue-initiation","cii"],["application/vnd.anser-web-funds-transfer-initiation","fti"],["application/vnd.antix.game-component","atx"],["application/vnd.apple.installer+xml","mpkg"],["application/vnd.apple.mpegurl","m3u8"],["application/vnd.aristanetworks.swi","swi"],["application/vnd.audiograph","aep"],["application/vnd.blueice.multipass","mpm"],["application/vnd.bmi","bmi"],["application/vnd.businessobjects","rep"],["application/vnd.chemdraw+xml","cdxml"],["application/vnd.chipnuts.karaoke-mmd","mmd"],["application/vnd.cinderella","cdy"],["application/vnd.claymore","cla"],["application/vnd.cloanto.rp9","rp9"],["application/vnd.clonk.c4group","c4g"],["application/vnd.cluetrust.cartomobile-config","c11amc"],["application/vnd.cluetrust.cartomobile-config-pkg","c11amz"],["application/vnd.commonspace","csp"],["application/vnd.contact.cmsg","cdbcmsg"],["application/vnd.cosmocaller","cmc"],["application/vnd.crick.clicker","clkx"],["application/vnd.crick.clicker.keyboard","clkk"],["application/vnd.crick.clicker.palette","clkp"],["application/vnd.crick.clicker.template","clkt"],["application/vnd.crick.clicker.wordbank","clkw"],["application/vnd.criticaltools.wbs+xml","wbs"],["application/vnd.ctc-posml","pml"],["application/vnd.cups-ppd","ppd"],["application/vnd.curl.car","car"],["application/vnd.curl.pcurl","pcurl"],["application/vnd.data-vision.rdz","rdz"],["application/vnd.denovo.fcselayout-link","fe_launch"],["application/vnd.dna","dna"],["application/vnd.dolby.mlp","mlp"],["application/vnd.dpgraph","dpg"],["application/vnd.dreamfactory","dfac"],["application/vnd.dvb.ait","ait"],["application/vnd.dvb.service","svc"],["application/vnd.dynageo","geo"],["application/vnd.ecowin.chart","mag"],["application/vnd.enliven","nml"],["application/vnd.epson.esf","esf"],["application/vnd.epson.msf","msf"],["application/vnd.epson.quickanime","qam"],["application/vnd.epson.salt","slt"],["application/vnd.epson.ssf","ssf"],["application/vnd.eszigno3+xml","es3"],["application/vnd.ezpix-album","ez2"],["application/vnd.ezpix-package","ez3"],["application/vnd.fdf","fdf"],["application/vnd.fdsn.seed","seed"],["application/vnd.flographit","gph"],["application/vnd.fluxtime.clip","ftc"],["application/vnd.framemaker","fm"],["application/vnd.frogans.fnc","fnc"],["application/vnd.frogans.ltf","ltf"],["application/vnd.fsc.weblaunch","fsc"],["application/vnd.fujitsu.oasys","oas"],["application/vnd.fujitsu.oasys2","oa2"],["application/vnd.fujitsu.oasys3","oa3"],["application/vnd.fujitsu.oasysgp","fg5"],["application/vnd.fujitsu.oasysprs","bh2"],["application/vnd.fujixerox.ddd","ddd"],["application/vnd.fujixerox.docuworks","xdw"],["application/vnd.fujixerox.docuworks.binder","xbd"],["application/vnd.fuzzysheet","fzs"],["application/vnd.genomatix.tuxedo","txd"],["application/vnd.geogebra.file","ggb"],["application/vnd.geogebra.tool","ggt"],["application/vnd.geometry-explorer","gex"],["application/vnd.geonext","gxt"],["application/vnd.geoplan","g2w"],["application/vnd.geospace","g3w"],["application/vnd.gmx","gmx"],["application/vnd.google-earth.kml+xml","kml"],["application/vnd.google-earth.kmz","kmz"],["application/vnd.grafeq","gqf"],["application/vnd.groove-account","gac"],["application/vnd.groove-help","ghf"],["application/vnd.groove-identity-message","gim"],["application/vnd.groove-injector","grv"],["application/vnd.groove-tool-message","gtm"],["application/vnd.groove-tool-template","tpl"],["application/vnd.groove-vcard","vcg"],["application/vnd.hal+xml","hal"],["application/vnd.handheld-entertainment+xml","zmm"],["application/vnd.hbci","hbci"],["application/vnd.hhe.lesson-player","les"],["application/vnd.hp-hpgl",["hgl","hpg","hpgl"]],["application/vnd.hp-hpid","hpid"],["application/vnd.hp-hps","hps"],["application/vnd.hp-jlyt","jlt"],["application/vnd.hp-pcl","pcl"],["application/vnd.hp-pclxl","pclxl"],["application/vnd.hydrostatix.sof-data","sfd-hdstx"],["application/vnd.hzn-3d-crossword","x3d"],["application/vnd.ibm.minipay","mpy"],["application/vnd.ibm.modcap","afp"],["application/vnd.ibm.rights-management","irm"],["application/vnd.ibm.secure-container","sc"],["application/vnd.iccprofile","icc"],["application/vnd.igloader","igl"],["application/vnd.immervision-ivp","ivp"],["application/vnd.immervision-ivu","ivu"],["application/vnd.insors.igm","igm"],["application/vnd.intercon.formnet","xpw"],["application/vnd.intergeo","i2g"],["application/vnd.intu.qbo","qbo"],["application/vnd.intu.qfx","qfx"],["application/vnd.ipunplugged.rcprofile","rcprofile"],["application/vnd.irepository.package+xml","irp"],["application/vnd.is-xpr","xpr"],["application/vnd.isac.fcs","fcs"],["application/vnd.jam","jam"],["application/vnd.jcp.javame.midlet-rms","rms"],["application/vnd.jisp","jisp"],["application/vnd.joost.joda-archive","joda"],["application/vnd.kahootz","ktz"],["application/vnd.kde.karbon","karbon"],["application/vnd.kde.kchart","chrt"],["application/vnd.kde.kformula","kfo"],["application/vnd.kde.kivio","flw"],["application/vnd.kde.kontour","kon"],["application/vnd.kde.kpresenter","kpr"],["application/vnd.kde.kspread","ksp"],["application/vnd.kde.kword","kwd"],["application/vnd.kenameaapp","htke"],["application/vnd.kidspiration","kia"],["application/vnd.kinar","kne"],["application/vnd.koan","skp"],["application/vnd.kodak-descriptor","sse"],["application/vnd.las.las+xml","lasxml"],["application/vnd.llamagraphics.life-balance.desktop","lbd"],["application/vnd.llamagraphics.life-balance.exchange+xml","lbe"],["application/vnd.lotus-1-2-3","123"],["application/vnd.lotus-approach","apr"],["application/vnd.lotus-freelance","pre"],["application/vnd.lotus-notes","nsf"],["application/vnd.lotus-organizer","org"],["application/vnd.lotus-screencam","scm"],["application/vnd.lotus-wordpro","lwp"],["application/vnd.macports.portpkg","portpkg"],["application/vnd.mcd","mcd"],["application/vnd.medcalcdata","mc1"],["application/vnd.mediastation.cdkey","cdkey"],["application/vnd.mfer","mwf"],["application/vnd.mfmp","mfm"],["application/vnd.micrografx.flo","flo"],["application/vnd.micrografx.igx","igx"],["application/vnd.mif","mif"],["application/vnd.mobius.daf","daf"],["application/vnd.mobius.dis","dis"],["application/vnd.mobius.mbk","mbk"],["application/vnd.mobius.mqy","mqy"],["application/vnd.mobius.msl","msl"],["application/vnd.mobius.plc","plc"],["application/vnd.mobius.txf","txf"],["application/vnd.mophun.application","mpn"],["application/vnd.mophun.certificate","mpc"],["application/vnd.mozilla.xul+xml","xul"],["application/vnd.ms-artgalry","cil"],["application/vnd.ms-cab-compressed","cab"],["application/vnd.ms-excel",["xls","xla","xlc","xlm","xlt","xlw","xlb","xll"]],["application/vnd.ms-excel.addin.macroenabled.12","xlam"],["application/vnd.ms-excel.sheet.binary.macroenabled.12","xlsb"],["application/vnd.ms-excel.sheet.macroenabled.12","xlsm"],["application/vnd.ms-excel.template.macroenabled.12","xltm"],["application/vnd.ms-fontobject","eot"],["application/vnd.ms-htmlhelp","chm"],["application/vnd.ms-ims","ims"],["application/vnd.ms-lrm","lrm"],["application/vnd.ms-officetheme","thmx"],["application/vnd.ms-outlook","msg"],["application/vnd.ms-pki.certstore","sst"],["application/vnd.ms-pki.pko","pko"],["application/vnd.ms-pki.seccat","cat"],["application/vnd.ms-pki.stl","stl"],["application/vnd.ms-pkicertstore","sst"],["application/vnd.ms-pkiseccat","cat"],["application/vnd.ms-pkistl","stl"],["application/vnd.ms-powerpoint",["ppt","pot","pps","ppa","pwz"]],["application/vnd.ms-powerpoint.addin.macroenabled.12","ppam"],["application/vnd.ms-powerpoint.presentation.macroenabled.12","pptm"],["application/vnd.ms-powerpoint.slide.macroenabled.12","sldm"],["application/vnd.ms-powerpoint.slideshow.macroenabled.12","ppsm"],["application/vnd.ms-powerpoint.template.macroenabled.12","potm"],["application/vnd.ms-project","mpp"],["application/vnd.ms-word.document.macroenabled.12","docm"],["application/vnd.ms-word.template.macroenabled.12","dotm"],["application/vnd.ms-works",["wks","wcm","wdb","wps"]],["application/vnd.ms-wpl","wpl"],["application/vnd.ms-xpsdocument","xps"],["application/vnd.mseq","mseq"],["application/vnd.musician","mus"],["application/vnd.muvee.style","msty"],["application/vnd.neurolanguage.nlu","nlu"],["application/vnd.noblenet-directory","nnd"],["application/vnd.noblenet-sealer","nns"],["application/vnd.noblenet-web","nnw"],["application/vnd.nokia.configuration-message","ncm"],["application/vnd.nokia.n-gage.data","ngdat"],["application/vnd.nokia.n-gage.symbian.install","n-gage"],["application/vnd.nokia.radio-preset","rpst"],["application/vnd.nokia.radio-presets","rpss"],["application/vnd.nokia.ringing-tone","rng"],["application/vnd.novadigm.edm","edm"],["application/vnd.novadigm.edx","edx"],["application/vnd.novadigm.ext","ext"],["application/vnd.oasis.opendocument.chart","odc"],["application/vnd.oasis.opendocument.chart-template","otc"],["application/vnd.oasis.opendocument.database","odb"],["application/vnd.oasis.opendocument.formula","odf"],["application/vnd.oasis.opendocument.formula-template","odft"],["application/vnd.oasis.opendocument.graphics","odg"],["application/vnd.oasis.opendocument.graphics-template","otg"],["application/vnd.oasis.opendocument.image","odi"],["application/vnd.oasis.opendocument.image-template","oti"],["application/vnd.oasis.opendocument.presentation","odp"],["application/vnd.oasis.opendocument.presentation-template","otp"],["application/vnd.oasis.opendocument.spreadsheet","ods"],["application/vnd.oasis.opendocument.spreadsheet-template","ots"],["application/vnd.oasis.opendocument.text","odt"],["application/vnd.oasis.opendocument.text-master","odm"],["application/vnd.oasis.opendocument.text-template","ott"],["application/vnd.oasis.opendocument.text-web","oth"],["application/vnd.olpc-sugar","xo"],["application/vnd.oma.dd2+xml","dd2"],["application/vnd.openofficeorg.extension","oxt"],["application/vnd.openxmlformats-officedocument.presentationml.presentation","pptx"],["application/vnd.openxmlformats-officedocument.presentationml.slide","sldx"],["application/vnd.openxmlformats-officedocument.presentationml.slideshow","ppsx"],["application/vnd.openxmlformats-officedocument.presentationml.template","potx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlsx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.template","xltx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.document","docx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.template","dotx"],["application/vnd.osgeo.mapguide.package","mgp"],["application/vnd.osgi.dp","dp"],["application/vnd.palm","pdb"],["application/vnd.pawaafile","paw"],["application/vnd.pg.format","str"],["application/vnd.pg.osasli","ei6"],["application/vnd.picsel","efif"],["application/vnd.pmi.widget","wg"],["application/vnd.pocketlearn","plf"],["application/vnd.powerbuilder6","pbd"],["application/vnd.previewsystems.box","box"],["application/vnd.proteus.magazine","mgz"],["application/vnd.publishare-delta-tree","qps"],["application/vnd.pvi.ptid1","ptid"],["application/vnd.quark.quarkxpress","qxd"],["application/vnd.realvnc.bed","bed"],["application/vnd.recordare.musicxml","mxl"],["application/vnd.recordare.musicxml+xml","musicxml"],["application/vnd.rig.cryptonote","cryptonote"],["application/vnd.rim.cod","cod"],["application/vnd.rn-realmedia","rm"],["application/vnd.rn-realplayer","rnx"],["application/vnd.route66.link66+xml","link66"],["application/vnd.sailingtracker.track","st"],["application/vnd.seemail","see"],["application/vnd.sema","sema"],["application/vnd.semd","semd"],["application/vnd.semf","semf"],["application/vnd.shana.informed.formdata","ifm"],["application/vnd.shana.informed.formtemplate","itp"],["application/vnd.shana.informed.interchange","iif"],["application/vnd.shana.informed.package","ipk"],["application/vnd.simtech-mindmapper","twd"],["application/vnd.smaf","mmf"],["application/vnd.smart.teacher","teacher"],["application/vnd.solent.sdkm+xml","sdkm"],["application/vnd.spotfire.dxp","dxp"],["application/vnd.spotfire.sfs","sfs"],["application/vnd.stardivision.calc","sdc"],["application/vnd.stardivision.draw","sda"],["application/vnd.stardivision.impress","sdd"],["application/vnd.stardivision.math","smf"],["application/vnd.stardivision.writer","sdw"],["application/vnd.stardivision.writer-global","sgl"],["application/vnd.stepmania.stepchart","sm"],["application/vnd.sun.xml.calc","sxc"],["application/vnd.sun.xml.calc.template","stc"],["application/vnd.sun.xml.draw","sxd"],["application/vnd.sun.xml.draw.template","std"],["application/vnd.sun.xml.impress","sxi"],["application/vnd.sun.xml.impress.template","sti"],["application/vnd.sun.xml.math","sxm"],["application/vnd.sun.xml.writer","sxw"],["application/vnd.sun.xml.writer.global","sxg"],["application/vnd.sun.xml.writer.template","stw"],["application/vnd.sus-calendar","sus"],["application/vnd.svd","svd"],["application/vnd.symbian.install","sis"],["application/vnd.syncml+xml","xsm"],["application/vnd.syncml.dm+wbxml","bdm"],["application/vnd.syncml.dm+xml","xdm"],["application/vnd.tao.intent-module-archive","tao"],["application/vnd.tmobile-livetv","tmo"],["application/vnd.trid.tpt","tpt"],["application/vnd.triscape.mxs","mxs"],["application/vnd.trueapp","tra"],["application/vnd.ufdl","ufd"],["application/vnd.uiq.theme","utz"],["application/vnd.umajin","umj"],["application/vnd.unity","unityweb"],["application/vnd.uoml+xml","uoml"],["application/vnd.vcx","vcx"],["application/vnd.visio","vsd"],["application/vnd.visionary","vis"],["application/vnd.vsf","vsf"],["application/vnd.wap.wbxml","wbxml"],["application/vnd.wap.wmlc","wmlc"],["application/vnd.wap.wmlscriptc","wmlsc"],["application/vnd.webturbo","wtb"],["application/vnd.wolfram.player","nbp"],["application/vnd.wordperfect","wpd"],["application/vnd.wqd","wqd"],["application/vnd.wt.stf","stf"],["application/vnd.xara",["web","xar"]],["application/vnd.xfdl","xfdl"],["application/vnd.yamaha.hv-dic","hvd"],["application/vnd.yamaha.hv-script","hvs"],["application/vnd.yamaha.hv-voice","hvp"],["application/vnd.yamaha.openscoreformat","osf"],["application/vnd.yamaha.openscoreformat.osfpvg+xml","osfpvg"],["application/vnd.yamaha.smaf-audio","saf"],["application/vnd.yamaha.smaf-phrase","spf"],["application/vnd.yellowriver-custom-menu","cmp"],["application/vnd.zul","zir"],["application/vnd.zzazz.deck+xml","zaz"],["application/vocaltec-media-desc","vmd"],["application/vocaltec-media-file","vmf"],["application/voicexml+xml","vxml"],["application/widget","wgt"],["application/winhlp","hlp"],["application/wordperfect",["wp","wp5","wp6","wpd"]],["application/wordperfect6.0",["w60","wp5"]],["application/wordperfect6.1","w61"],["application/wsdl+xml","wsdl"],["application/wspolicy+xml","wspolicy"],["application/x-123","wk1"],["application/x-7z-compressed","7z"],["application/x-abiword","abw"],["application/x-ace-compressed","ace"],["application/x-aim","aim"],["application/x-authorware-bin","aab"],["application/x-authorware-map","aam"],["application/x-authorware-seg","aas"],["application/x-bcpio","bcpio"],["application/x-binary","bin"],["application/x-binhex40","hqx"],["application/x-bittorrent","torrent"],["application/x-bsh",["bsh","sh","shar"]],["application/x-bytecode.elisp","elc"],["application/x-bytecode.python","pyc"],["application/x-bzip","bz"],["application/x-bzip2",["boz","bz2"]],["application/x-cdf","cdf"],["application/x-cdlink","vcd"],["application/x-chat",["cha","chat"]],["application/x-chess-pgn","pgn"],["application/x-cmu-raster","ras"],["application/x-cocoa","cco"],["application/x-compactpro","cpt"],["application/x-compress","z"],["application/x-compressed",["tgz","gz","z","zip"]],["application/x-conference","nsc"],["application/x-cpio","cpio"],["application/x-cpt","cpt"],["application/x-csh","csh"],["application/x-debian-package","deb"],["application/x-deepv","deepv"],["application/x-director",["dir","dcr","dxr"]],["application/x-doom","wad"],["application/x-dtbncx+xml","ncx"],["application/x-dtbook+xml","dtb"],["application/x-dtbresource+xml","res"],["application/x-dvi","dvi"],["application/x-elc","elc"],["application/x-envoy",["env","evy"]],["application/x-esrehber","es"],["application/x-excel",["xls","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/x-font-bdf","bdf"],["application/x-font-ghostscript","gsf"],["application/x-font-linux-psf","psf"],["application/x-font-otf","otf"],["application/x-font-pcf","pcf"],["application/x-font-snf","snf"],["application/x-font-ttf","ttf"],["application/x-font-type1","pfa"],["application/x-font-woff","woff"],["application/x-frame","mif"],["application/x-freelance","pre"],["application/x-futuresplash","spl"],["application/x-gnumeric","gnumeric"],["application/x-gsp","gsp"],["application/x-gss","gss"],["application/x-gtar","gtar"],["application/x-gzip",["gz","gzip"]],["application/x-hdf","hdf"],["application/x-helpfile",["help","hlp"]],["application/x-httpd-imap","imap"],["application/x-ima","ima"],["application/x-internet-signup",["ins","isp"]],["application/x-internett-signup","ins"],["application/x-inventor","iv"],["application/x-ip2","ip"],["application/x-iphone","iii"],["application/x-java-class","class"],["application/x-java-commerce","jcm"],["application/x-java-jnlp-file","jnlp"],["application/x-javascript","js"],["application/x-koan",["skd","skm","skp","skt"]],["application/x-ksh","ksh"],["application/x-latex",["latex","ltx"]],["application/x-lha","lha"],["application/x-lisp","lsp"],["application/x-livescreen","ivy"],["application/x-lotus","wq1"],["application/x-lotusscreencam","scm"],["application/x-lzh","lzh"],["application/x-lzx","lzx"],["application/x-mac-binhex40","hqx"],["application/x-macbinary","bin"],["application/x-magic-cap-package-1.0","mc$"],["application/x-mathcad","mcd"],["application/x-meme","mm"],["application/x-midi",["mid","midi"]],["application/x-mif","mif"],["application/x-mix-transfer","nix"],["application/x-mobipocket-ebook","prc"],["application/x-mplayer2","asx"],["application/x-ms-application","application"],["application/x-ms-wmd","wmd"],["application/x-ms-wmz","wmz"],["application/x-ms-xbap","xbap"],["application/x-msaccess","mdb"],["application/x-msbinder","obd"],["application/x-mscardfile","crd"],["application/x-msclip","clp"],["application/x-msdownload",["exe","dll"]],["application/x-msexcel",["xls","xla","xlw"]],["application/x-msmediaview",["mvb","m13","m14"]],["application/x-msmetafile","wmf"],["application/x-msmoney","mny"],["application/x-mspowerpoint","ppt"],["application/x-mspublisher","pub"],["application/x-msschedule","scd"],["application/x-msterminal","trm"],["application/x-mswrite","wri"],["application/x-navi-animation","ani"],["application/x-navidoc","nvd"],["application/x-navimap","map"],["application/x-navistyle","stl"],["application/x-netcdf",["cdf","nc"]],["application/x-newton-compatible-pkg","pkg"],["application/x-nokia-9000-communicator-add-on-software","aos"],["application/x-omc","omc"],["application/x-omcdatamaker","omcd"],["application/x-omcregerator","omcr"],["application/x-pagemaker",["pm4","pm5"]],["application/x-pcl","pcl"],["application/x-perfmon",["pma","pmc","pml","pmr","pmw"]],["application/x-pixclscript","plx"],["application/x-pkcs10","p10"],["application/x-pkcs12",["p12","pfx"]],["application/x-pkcs7-certificates",["p7b","spc"]],["application/x-pkcs7-certreqresp","p7r"],["application/x-pkcs7-mime",["p7m","p7c"]],["application/x-pkcs7-signature",["p7s","p7a"]],["application/x-pointplus","css"],["application/x-portable-anymap","pnm"],["application/x-project",["mpc","mpt","mpv","mpx"]],["application/x-qpro","wb1"],["application/x-rar-compressed","rar"],["application/x-rtf","rtf"],["application/x-sdp","sdp"],["application/x-sea","sea"],["application/x-seelogo","sl"],["application/x-sh","sh"],["application/x-shar",["shar","sh"]],["application/x-shockwave-flash","swf"],["application/x-silverlight-app","xap"],["application/x-sit","sit"],["application/x-sprite",["spr","sprite"]],["application/x-stuffit","sit"],["application/x-stuffitx","sitx"],["application/x-sv4cpio","sv4cpio"],["application/x-sv4crc","sv4crc"],["application/x-tar","tar"],["application/x-tbook",["sbk","tbk"]],["application/x-tcl","tcl"],["application/x-tex","tex"],["application/x-tex-tfm","tfm"],["application/x-texinfo",["texi","texinfo"]],["application/x-troff",["roff","t","tr"]],["application/x-troff-man","man"],["application/x-troff-me","me"],["application/x-troff-ms","ms"],["application/x-troff-msvideo","avi"],["application/x-ustar","ustar"],["application/x-visio",["vsd","vst","vsw"]],["application/x-vnd.audioexplosion.mzz","mzz"],["application/x-vnd.ls-xpix","xpix"],["application/x-vrml","vrml"],["application/x-wais-source",["src","wsrc"]],["application/x-winhelp","hlp"],["application/x-wintalk","wtk"],["application/x-world",["wrl","svr"]],["application/x-wpwin","wpd"],["application/x-wri","wri"],["application/x-x509-ca-cert",["cer","crt","der"]],["application/x-x509-user-cert","crt"],["application/x-xfig","fig"],["application/x-xpinstall","xpi"],["application/x-zip-compressed","zip"],["application/xcap-diff+xml","xdf"],["application/xenc+xml","xenc"],["application/xhtml+xml","xhtml"],["application/xml","xml"],["application/xml-dtd","dtd"],["application/xop+xml","xop"],["application/xslt+xml","xslt"],["application/xspf+xml","xspf"],["application/xv+xml","mxml"],["application/yang","yang"],["application/yin+xml","yin"],["application/ynd.ms-pkipko","pko"],["application/zip","zip"],["audio/adpcm","adp"],["audio/aiff",["aiff","aif","aifc"]],["audio/basic",["snd","au"]],["audio/it","it"],["audio/make",["funk","my","pfunk"]],["audio/make.my.funk","pfunk"],["audio/mid",["mid","rmi"]],["audio/midi",["midi","kar","mid"]],["audio/mod","mod"],["audio/mp4","mp4a"],["audio/mpeg",["mpga","mp3","m2a","mp2","mpa","mpg"]],["audio/mpeg3","mp3"],["audio/nspaudio",["la","lma"]],["audio/ogg","oga"],["audio/s3m","s3m"],["audio/tsp-audio","tsi"],["audio/tsplayer","tsp"],["audio/vnd.dece.audio","uva"],["audio/vnd.digital-winds","eol"],["audio/vnd.dra","dra"],["audio/vnd.dts","dts"],["audio/vnd.dts.hd","dtshd"],["audio/vnd.lucent.voice","lvp"],["audio/vnd.ms-playready.media.pya","pya"],["audio/vnd.nuera.ecelp4800","ecelp4800"],["audio/vnd.nuera.ecelp7470","ecelp7470"],["audio/vnd.nuera.ecelp9600","ecelp9600"],["audio/vnd.qcelp","qcp"],["audio/vnd.rip","rip"],["audio/voc","voc"],["audio/voxware","vox"],["audio/wav","wav"],["audio/webm","weba"],["audio/x-aac","aac"],["audio/x-adpcm","snd"],["audio/x-aiff",["aiff","aif","aifc"]],["audio/x-au","au"],["audio/x-gsm",["gsd","gsm"]],["audio/x-jam","jam"],["audio/x-liveaudio","lam"],["audio/x-mid",["mid","midi"]],["audio/x-midi",["midi","mid"]],["audio/x-mod","mod"],["audio/x-mpeg","mp2"],["audio/x-mpeg-3","mp3"],["audio/x-mpegurl","m3u"],["audio/x-mpequrl","m3u"],["audio/x-ms-wax","wax"],["audio/x-ms-wma","wma"],["audio/x-nspaudio",["la","lma"]],["audio/x-pn-realaudio",["ra","ram","rm","rmm","rmp"]],["audio/x-pn-realaudio-plugin",["ra","rmp","rpm"]],["audio/x-psid","sid"],["audio/x-realaudio","ra"],["audio/x-twinvq","vqf"],["audio/x-twinvq-plugin",["vqe","vql"]],["audio/x-vnd.audioexplosion.mjuicemediafile","mjf"],["audio/x-voc","voc"],["audio/x-wav","wav"],["audio/xm","xm"],["chemical/x-cdx","cdx"],["chemical/x-cif","cif"],["chemical/x-cmdf","cmdf"],["chemical/x-cml","cml"],["chemical/x-csml","csml"],["chemical/x-pdb",["pdb","xyz"]],["chemical/x-xyz","xyz"],["drawing/x-dwf","dwf"],["i-world/i-vrml","ivr"],["image/bmp",["bmp","bm"]],["image/cgm","cgm"],["image/cis-cod","cod"],["image/cmu-raster",["ras","rast"]],["image/fif","fif"],["image/florian",["flo","turbot"]],["image/g3fax","g3"],["image/gif","gif"],["image/ief",["ief","iefs"]],["image/jpeg",["jpeg","jpe","jpg","jfif","jfif-tbnl"]],["image/jutvision","jut"],["image/ktx","ktx"],["image/naplps",["nap","naplps"]],["image/pict",["pic","pict"]],["image/pipeg","jfif"],["image/pjpeg",["jfif","jpe","jpeg","jpg"]],["image/png",["png","x-png"]],["image/prs.btif","btif"],["image/svg+xml","svg"],["image/tiff",["tif","tiff"]],["image/vasa","mcf"],["image/vnd.adobe.photoshop","psd"],["image/vnd.dece.graphic","uvi"],["image/vnd.djvu","djvu"],["image/vnd.dvb.subtitle","sub"],["image/vnd.dwg",["dwg","dxf","svf"]],["image/vnd.dxf","dxf"],["image/vnd.fastbidsheet","fbs"],["image/vnd.fpx","fpx"],["image/vnd.fst","fst"],["image/vnd.fujixerox.edmics-mmr","mmr"],["image/vnd.fujixerox.edmics-rlc","rlc"],["image/vnd.ms-modi","mdi"],["image/vnd.net-fpx",["fpx","npx"]],["image/vnd.rn-realflash","rf"],["image/vnd.rn-realpix","rp"],["image/vnd.wap.wbmp","wbmp"],["image/vnd.xiff","xif"],["image/webp","webp"],["image/x-cmu-raster","ras"],["image/x-cmx","cmx"],["image/x-dwg",["dwg","dxf","svf"]],["image/x-freehand","fh"],["image/x-icon","ico"],["image/x-jg","art"],["image/x-jps","jps"],["image/x-niff",["niff","nif"]],["image/x-pcx","pcx"],["image/x-pict",["pct","pic"]],["image/x-portable-anymap","pnm"],["image/x-portable-bitmap","pbm"],["image/x-portable-graymap","pgm"],["image/x-portable-greymap","pgm"],["image/x-portable-pixmap","ppm"],["image/x-quicktime",["qif","qti","qtif"]],["image/x-rgb","rgb"],["image/x-tiff",["tif","tiff"]],["image/x-windows-bmp","bmp"],["image/x-xbitmap","xbm"],["image/x-xbm","xbm"],["image/x-xpixmap",["xpm","pm"]],["image/x-xwd","xwd"],["image/x-xwindowdump","xwd"],["image/xbm","xbm"],["image/xpm","xpm"],["message/rfc822",["eml","mht","mhtml","nws","mime"]],["model/iges",["iges","igs"]],["model/mesh","msh"],["model/vnd.collada+xml","dae"],["model/vnd.dwf","dwf"],["model/vnd.gdl","gdl"],["model/vnd.gtw","gtw"],["model/vnd.mts","mts"],["model/vnd.vtu","vtu"],["model/vrml",["vrml","wrl","wrz"]],["model/x-pov","pov"],["multipart/x-gzip","gzip"],["multipart/x-ustar","ustar"],["multipart/x-zip","zip"],["music/crescendo",["mid","midi"]],["music/x-karaoke","kar"],["paleovu/x-pv","pvu"],["text/asp","asp"],["text/calendar","ics"],["text/css","css"],["text/csv","csv"],["text/ecmascript","js"],["text/h323","323"],["text/html",["html","htm","stm","acgi","htmls","htx","shtml"]],["text/iuls","uls"],["text/javascript","js"],["text/mcf","mcf"],["text/n3","n3"],["text/pascal","pas"],["text/plain",["txt","bas","c","h","c++","cc","com","conf","cxx","def","f","f90","for","g","hh","idc","jav","java","list","log","lst","m","mar","pl","sdml","text"]],["text/plain-bas","par"],["text/prs.lines.tag","dsc"],["text/richtext",["rtx","rt","rtf"]],["text/scriplet","wsc"],["text/scriptlet","sct"],["text/sgml",["sgm","sgml"]],["text/tab-separated-values","tsv"],["text/troff","t"],["text/turtle","ttl"],["text/uri-list",["uni","unis","uri","uris"]],["text/vnd.abc","abc"],["text/vnd.curl","curl"],["text/vnd.curl.dcurl","dcurl"],["text/vnd.curl.mcurl","mcurl"],["text/vnd.curl.scurl","scurl"],["text/vnd.fly","fly"],["text/vnd.fmi.flexstor","flx"],["text/vnd.graphviz","gv"],["text/vnd.in3d.3dml","3dml"],["text/vnd.in3d.spot","spot"],["text/vnd.rn-realtext","rt"],["text/vnd.sun.j2me.app-descriptor","jad"],["text/vnd.wap.wml","wml"],["text/vnd.wap.wmlscript","wmls"],["text/webviewhtml","htt"],["text/x-asm",["asm","s"]],["text/x-audiosoft-intra","aip"],["text/x-c",["c","cc","cpp"]],["text/x-component","htc"],["text/x-fortran",["for","f","f77","f90"]],["text/x-h",["h","hh"]],["text/x-java-source",["java","jav"]],["text/x-java-source,java","java"],["text/x-la-asf","lsx"],["text/x-m","m"],["text/x-pascal","p"],["text/x-script","hlb"],["text/x-script.csh","csh"],["text/x-script.elisp","el"],["text/x-script.guile","scm"],["text/x-script.ksh","ksh"],["text/x-script.lisp","lsp"],["text/x-script.perl","pl"],["text/x-script.perl-module","pm"],["text/x-script.phyton","py"],["text/x-script.rexx","rexx"],["text/x-script.scheme","scm"],["text/x-script.sh","sh"],["text/x-script.tcl","tcl"],["text/x-script.tcsh","tcsh"],["text/x-script.zsh","zsh"],["text/x-server-parsed-html",["shtml","ssi"]],["text/x-setext","etx"],["text/x-sgml",["sgm","sgml"]],["text/x-speech",["spc","talk"]],["text/x-uil","uil"],["text/x-uuencode",["uu","uue"]],["text/x-vcalendar","vcs"],["text/x-vcard","vcf"],["text/xml","xml"],["video/3gpp","3gp"],["video/3gpp2","3g2"],["video/animaflex","afl"],["video/avi","avi"],["video/avs-video","avs"],["video/dl","dl"],["video/fli","fli"],["video/gl","gl"],["video/h261","h261"],["video/h263","h263"],["video/h264","h264"],["video/jpeg","jpgv"],["video/jpm","jpm"],["video/mj2","mj2"],["video/mp4","mp4"],["video/mpeg",["mpeg","mp2","mpa","mpe","mpg","mpv2","m1v","m2v","mp3"]],["video/msvideo","avi"],["video/ogg","ogv"],["video/quicktime",["mov","qt","moov"]],["video/vdo","vdo"],["video/vivo",["viv","vivo"]],["video/vnd.dece.hd","uvh"],["video/vnd.dece.mobile","uvm"],["video/vnd.dece.pd","uvp"],["video/vnd.dece.sd","uvs"],["video/vnd.dece.video","uvv"],["video/vnd.fvt","fvt"],["video/vnd.mpegurl","mxu"],["video/vnd.ms-playready.media.pyv","pyv"],["video/vnd.rn-realvideo","rv"],["video/vnd.uvvu.mp4","uvu"],["video/vnd.vivo",["viv","vivo"]],["video/vosaic","vos"],["video/webm","webm"],["video/x-amt-demorun","xdr"],["video/x-amt-showrun","xsr"],["video/x-atomic3d-feature","fmf"],["video/x-dl","dl"],["video/x-dv",["dif","dv"]],["video/x-f4v","f4v"],["video/x-fli","fli"],["video/x-flv","flv"],["video/x-gl","gl"],["video/x-isvideo","isu"],["video/x-la-asf",["lsf","lsx"]],["video/x-m4v","m4v"],["video/x-motion-jpeg","mjpg"],["video/x-mpeg",["mp3","mp2"]],["video/x-mpeq2a","mp2"],["video/x-ms-asf",["asf","asr","asx"]],["video/x-ms-asf-plugin","asx"],["video/x-ms-wm","wm"],["video/x-ms-wmv","wmv"],["video/x-ms-wmx","wmx"],["video/x-ms-wvx","wvx"],["video/x-msvideo","avi"],["video/x-qtc","qtc"],["video/x-scm","scm"],["video/x-sgi-movie",["movie","mv"]],["windows/metafile","wmf"],["www/mime","mime"],["x-conference/x-cooltalk","ice"],["x-music/x-midi",["mid","midi"]],["x-world/x-3dmf",["3dm","3dmf","qd3","qd3d"]],["x-world/x-svr","svr"],["x-world/x-vrml",["flr","vrml","wrl","wrz","xaf","xof"]],["x-world/x-vrt","vrt"],["xgl/drawing","xgz"],["xgl/movie","xmz"]]),o=new Map([["123","application/vnd.lotus-1-2-3"],["323","text/h323"],["*","application/octet-stream"],["3dm","x-world/x-3dmf"],["3dmf","x-world/x-3dmf"],["3dml","text/vnd.in3d.3dml"],["3g2","video/3gpp2"],["3gp","video/3gpp"],["7z","application/x-7z-compressed"],["a","application/octet-stream"],["aab","application/x-authorware-bin"],["aac","audio/x-aac"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abc","text/vnd.abc"],["abw","application/x-abiword"],["ac","application/pkix-attr-cert"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acgi","text/html"],["acu","application/vnd.acucobol"],["acx","application/internet-property-stream"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afl","video/animaflex"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/postscript"],["aif",["audio/aiff","audio/x-aiff"]],["aifc",["audio/aiff","audio/x-aiff"]],["aiff",["audio/aiff","audio/x-aiff"]],["aim","application/x-aim"],["aip","text/x-audiosoft-intra"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["ani","application/x-navi-animation"],["aos","application/x-nokia-9000-communicator-add-on-software"],["apk","application/vnd.android.package-archive"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["aps","application/mime"],["arc","application/octet-stream"],["arj",["application/arj","application/octet-stream"]],["art","image/x-jg"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asp","text/asp"],["asr","video/x-ms-asf"],["asx",["video/x-ms-asf","application/x-mplayer2","video/x-ms-asf-plugin"]],["atc","application/vnd.acucorp"],["atomcat","application/atomcat+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au",["audio/basic","audio/x-au"]],["avi",["video/avi","video/msvideo","application/x-troff-msvideo","video/x-msvideo"]],["avs","video/avs-video"],["aw","application/applixware"],["axs","application/olescript"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azw","application/vnd.amazon.ebook"],["bas","text/plain"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin",["application/octet-stream","application/mac-binary","application/macbinary","application/x-macbinary","application/x-binary"]],["bm","image/bmp"],["bmi","application/vnd.bmi"],["bmp",["image/bmp","image/x-windows-bmp"]],["boo","application/book"],["book","application/book"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bsh","application/x-bsh"],["btif","image/prs.btif"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c",["text/plain","text/x-c"]],["c++","text/plain"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["c4g","application/vnd.clonk.c4group"],["cab","application/vnd.ms-cab-compressed"],["car","application/vnd.curl.car"],["cat",["application/vnd.ms-pkiseccat","application/vnd.ms-pki.seccat"]],["cc",["text/plain","text/x-c"]],["ccad","application/clariscad"],["cco","application/x-cocoa"],["ccxml","application/ccxml+xml,"],["cdbcmsg","application/vnd.contact.cmsg"],["cdf",["application/cdf","application/x-cdf","application/x-netcdf"]],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer",["application/pkix-cert","application/x-x509-ca-cert"]],["cgm","image/cgm"],["cha","application/x-chat"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cla","application/vnd.claymore"],["class",["application/octet-stream","application/java","application/java-byte-code","application/java-vm","application/x-java-class"]],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod",["image/cis-cod","application/vnd.rim.cod"]],["com",["application/octet-stream","text/plain"]],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt",["application/mac-compactpro","application/x-compactpro","application/x-cpt"]],["crd","application/x-mscardfile"],["crl",["application/pkix-crl","application/pkcs-crl"]],["crt",["application/pkix-cert","application/x-x509-user-cert","application/x-x509-ca-cert"]],["cryptonote","application/vnd.rig.cryptonote"],["csh",["text/x-script.csh","application/x-csh"]],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["css",["text/css","application/x-pointplus"]],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxx","text/plain"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["davmount","application/davmount+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["deb","application/x-debian-package"],["deepv","application/x-deepv"],["def","text/plain"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dif","video/x-dv"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["djvu","image/vnd.djvu"],["dl",["video/dl","video/x-dl"]],["dll","application/x-msdownload"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.document.macroenabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroenabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp",["application/commonground","application/vnd.osgi.dp"]],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drw","application/drafting"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dv","video/x-dv"],["dvi","application/x-dvi"],["dwf",["model/vnd.dwf","drawing/x-dwf"]],["dwg",["application/acad","image/vnd.dwg","image/x-dwg"]],["dxf",["application/dxf","image/vnd.dwg","image/vnd.dxf","image/x-dwg"]],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["el","text/x-script.elisp"],["elc",["application/x-elc","application/x-bytecode.elisp"]],["eml","message/rfc822"],["emma","application/emma+xml"],["env","application/x-envoy"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es",["application/ecmascript","application/x-esrehber"]],["es3","application/vnd.eszigno3+xml"],["esf","application/vnd.epson.esf"],["etx","text/x-setext"],["evy",["application/envoy","application/x-envoy"]],["exe",["application/octet-stream","application/x-msdownload"]],["exi","application/exi"],["ext","application/vnd.novadigm.ext"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f",["text/plain","text/x-fortran"]],["f4v","video/x-f4v"],["f77","text/x-fortran"],["f90",["text/plain","text/x-fortran"]],["fbs","image/vnd.fastbidsheet"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fh","image/x-freehand"],["fif",["application/fractals","image/fif"]],["fig","application/x-xfig"],["fli",["video/fli","video/x-fli"]],["flo",["image/florian","application/vnd.micrografx.flo"]],["flr","x-world/x-vrml"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fmf","video/x-atomic3d-feature"],["fnc","application/vnd.frogans.fnc"],["for",["text/plain","text/x-fortran"]],["fpx",["image/vnd.fpx","image/vnd.net-fpx"]],["frl","application/freeloader"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["funk","audio/make"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g","text/plain"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gdl","model/vnd.gdl"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["gl",["video/gl","video/x-gl"]],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gph","application/vnd.flographit"],["gqf","application/vnd.grafeq"],["gram","application/srgs"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsd","audio/x-gsm"],["gsf","application/x-font-ghostscript"],["gsm","audio/x-gsm"],["gsp","application/x-gsp"],["gss","application/x-gss"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxt","application/vnd.geonext"],["gz",["application/x-gzip","application/x-compressed"]],["gzip",["multipart/x-gzip","application/x-gzip"]],["h",["text/plain","text/x-h"]],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hdf","application/x-hdf"],["help","application/x-helpfile"],["hgl","application/vnd.hp-hpgl"],["hh",["text/plain","text/x-h"]],["hlb","text/x-script"],["hlp",["application/winhlp","application/hlp","application/x-helpfile","application/x-winhelp"]],["hpg","application/vnd.hp-hpgl"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx",["application/mac-binhex40","application/binhex","application/binhex4","application/mac-binhex","application/x-binhex40","application/x-mac-binhex40"]],["hta","application/hta"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["htmls","text/html"],["htt","text/webviewhtml"],["htx","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["ico","image/x-icon"],["ics","text/calendar"],["idc","text/plain"],["ief","image/ief"],["iefs","image/ief"],["ifm","application/vnd.shana.informed.formdata"],["iges",["application/iges","model/iges"]],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs",["application/iges","model/iges"]],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["iii","application/x-iphone"],["ima","application/x-ima"],["imap","application/x-httpd-imap"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["inf","application/inf"],["ins",["application/x-internet-signup","application/x-internett-signup"]],["ip","application/x-ip2"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["isp","application/x-internet-signup"],["isu","video/x-isvideo"],["it","audio/it"],["itp","application/vnd.shana.informed.formtemplate"],["iv","application/x-inventor"],["ivp","application/vnd.immervision-ivp"],["ivr","i-world/i-vrml"],["ivu","application/vnd.immervision-ivu"],["ivy","application/x-livescreen"],["jad","text/vnd.sun.j2me.app-descriptor"],["jam",["application/vnd.jam","audio/x-jam"]],["jar","application/java-archive"],["jav",["text/plain","text/x-java-source"]],["java",["text/plain","text/x-java-source,java","text/x-java-source"]],["jcm","application/x-java-commerce"],["jfif",["image/pipeg","image/jpeg","image/pjpeg"]],["jfif-tbnl","image/jpeg"],["jisp","application/vnd.jisp"],["jlt","application/vnd.hp-jlyt"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jpe",["image/jpeg","image/pjpeg"]],["jpeg",["image/jpeg","image/pjpeg"]],["jpg",["image/jpeg","image/pjpeg"]],["jpgv","video/jpeg"],["jpm","video/jpm"],["jps","image/x-jps"],["js",["application/javascript","application/ecmascript","text/javascript","text/ecmascript","application/x-javascript"]],["json","application/json"],["jut","image/jutvision"],["kar",["audio/midi","music/x-karaoke"]],["karbon","application/vnd.kde.karbon"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["ksh",["application/x-ksh","text/x-script.ksh"]],["ksp","application/vnd.kde.kspread"],["ktx","image/ktx"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["la",["audio/nspaudio","audio/x-nspaudio"]],["lam","audio/x-liveaudio"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["lha",["application/octet-stream","application/lha","application/x-lha"]],["lhx","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["lma",["audio/nspaudio","audio/x-nspaudio"]],["log","text/plain"],["lrm","application/vnd.ms-lrm"],["lsf","video/x-la-asf"],["lsp",["application/x-lisp","text/x-script.lisp"]],["lst","text/plain"],["lsx",["video/x-la-asf","text/x-la-asf"]],["ltf","application/vnd.frogans.ltf"],["ltx","application/x-latex"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh",["application/octet-stream","application/x-lzh"]],["lzx",["application/lzx","application/octet-stream","application/x-lzx"]],["m",["text/plain","text/x-m"]],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m1v","video/mpeg"],["m21","application/mp21"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3u",["audio/x-mpegurl","audio/x-mpequrl"]],["m3u8","application/vnd.apple.mpegurl"],["m4v","video/x-m4v"],["ma","application/mathematica"],["mads","application/mads+xml"],["mag","application/vnd.ecowin.chart"],["man","application/x-troff-man"],["map","application/x-navimap"],["mar","text/plain"],["mathml","application/mathml+xml"],["mbd","application/mbedlet"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc$","application/x-magic-cap-package-1.0"],["mc1","application/vnd.medcalcdata"],["mcd",["application/mcad","application/vnd.mcd","application/x-mathcad"]],["mcf",["image/vasa","text/mcf"]],["mcp","application/netmc"],["mcurl","text/vnd.curl.mcurl"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["me","application/x-troff-me"],["meta4","application/metalink4+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mht","message/rfc822"],["mhtml","message/rfc822"],["mid",["audio/mid","audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["midi",["audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["mif",["application/vnd.mif","application/x-mif","application/x-frame"]],["mime",["message/rfc822","www/mime"]],["mj2","video/mj2"],["mjf","audio/x-vnd.audioexplosion.mjuicemediafile"],["mjpg","video/x-motion-jpeg"],["mlp","application/vnd.dolby.mlp"],["mm",["application/base64","application/x-meme"]],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mme","application/base64"],["mmf","application/vnd.smaf"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mny","application/x-msmoney"],["mod",["audio/mod","audio/x-mod"]],["mods","application/mods+xml"],["moov","video/quicktime"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2",["video/mpeg","audio/mpeg","video/x-mpeg","audio/x-mpeg","video/x-mpeq2a"]],["mp3",["audio/mpeg","audio/mpeg3","video/mpeg","audio/x-mpeg-3","video/x-mpeg"]],["mp4",["video/mp4","application/mp4"]],["mp4a","audio/mp4"],["mpa",["video/mpeg","audio/mpeg"]],["mpc",["application/vnd.mophun.certificate","application/x-project"]],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg",["video/mpeg","audio/mpeg"]],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/x-project"],["mpv","application/x-project"],["mpv2","video/mpeg"],["mpx","application/x-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","application/x-troff-ms"],["mscml","application/mediaservercontrol+xml"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msl","application/vnd.mobius.msl"],["msty","application/vnd.muvee.style"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musicxml","application/vnd.recordare.musicxml+xml"],["mv","video/x-sgi-movie"],["mvb","application/x-msmediaview"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["my","audio/make"],["mzz","application/x-vnd.audioexplosion.mzz"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nap","image/naplps"],["naplps","image/naplps"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncm","application/vnd.nokia.configuration-message"],["ncx","application/x-dtbncx+xml"],["ngdat","application/vnd.nokia.n-gage.data"],["nif","image/x-niff"],["niff","image/x-niff"],["nix","application/x-mix-transfer"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nvd","application/x-navidoc"],["nws","message/rfc822"],["o","application/octet-stream"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omc","application/x-omc"],["omcd","application/x-omcdatamaker"],["omcr","application/x-omcregerator"],["onetoc","application/onenote"],["opf","application/oebps-package+xml"],["org","application/vnd.lotus-organizer"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","application/x-font-otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p10",["application/pkcs10","application/x-pkcs10"]],["p12",["application/pkcs-12","application/x-pkcs12"]],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7m",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7r","application/x-pkcs7-certreqresp"],["p7s",["application/pkcs7-signature","application/x-pkcs7-signature"]],["p8","application/pkcs8"],["par","text/plain-bas"],["part","application/pro_eng"],["pas","text/pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcf","application/x-font-pcf"],["pcl",["application/vnd.hp-pcl","application/x-pcl"]],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb",["application/vnd.palm","chemical/x-pdb"]],["pdf","application/pdf"],["pfa","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfunk",["audio/make","audio/make.my.funk"]],["pfx","application/x-pkcs12"],["pgm",["image/x-portable-graymap","image/x-portable-greymap"]],["pgn","application/x-chess-pgn"],["pgp","application/pgp-signature"],["pic",["image/pict","image/x-pict"]],["pict","image/pict"],["pkg","application/x-newton-compatible-pkg"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pko",["application/ynd.ms-pkipko","application/vnd.ms-pki.pko"]],["pl",["text/plain","text/x-script.perl"]],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["plx","application/x-pixclscript"],["pm",["text/x-script.perl-module","image/x-xpixmap"]],["pm4","application/x-pagemaker"],["pm5","application/x-pagemaker"],["pma","application/x-perfmon"],["pmc","application/x-perfmon"],["pml",["application/vnd.ctc-posml","application/x-perfmon"]],["pmr","application/x-perfmon"],["pmw","application/x-perfmon"],["png","image/png"],["pnm",["application/x-portable-anymap","image/x-portable-anymap"]],["portpkg","application/vnd.macports.portpkg"],["pot",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["potm","application/vnd.ms-powerpoint.template.macroenabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["pov","model/x-pov"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroenabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["ppsm","application/vnd.ms-powerpoint.slideshow.macroenabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt",["application/vnd.ms-powerpoint","application/mspowerpoint","application/powerpoint","application/x-mspowerpoint"]],["pptm","application/vnd.ms-powerpoint.presentation.macroenabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["ppz","application/mspowerpoint"],["prc","application/x-mobipocket-ebook"],["pre",["application/vnd.lotus-freelance","application/x-freelance"]],["prf","application/pics-rules"],["prt","application/pro_eng"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd",["application/octet-stream","image/vnd.adobe.photoshop"]],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pvu","paleovu/x-pv"],["pwn","application/vnd.3m.post-it-notes"],["pwz","application/vnd.ms-powerpoint"],["py","text/x-script.phyton"],["pya","audio/vnd.ms-playready.media.pya"],["pyc","application/x-bytecode.python"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qcp","audio/vnd.qcelp"],["qd3","x-world/x-3dmf"],["qd3d","x-world/x-3dmf"],["qfx","application/vnd.intu.qfx"],["qif","image/x-quicktime"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qtc","video/x-qtc"],["qti","image/x-quicktime"],["qtif","image/x-quicktime"],["qxd","application/vnd.quark.quarkxpress"],["ra",["audio/x-realaudio","audio/x-pn-realaudio","audio/x-pn-realaudio-plugin"]],["ram","audio/x-pn-realaudio"],["rar","application/x-rar-compressed"],["ras",["image/cmu-raster","application/x-cmu-raster","image/x-cmu-raster"]],["rast","image/cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rexx","text/x-script.rexx"],["rf","image/vnd.rn-realflash"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm",["application/vnd.rn-realmedia","audio/x-pn-realaudio"]],["rmi","audio/mid"],["rmm","audio/x-pn-realaudio"],["rmp",["audio/x-pn-realaudio-plugin","audio/x-pn-realaudio"]],["rms","application/vnd.jcp.javame.midlet-rms"],["rnc","application/relax-ng-compact-syntax"],["rng",["application/ringing-tones","application/vnd.nokia.ringing-tone"]],["rnx","application/vnd.rn-realplayer"],["roff","application/x-troff"],["rp","image/vnd.rn-realpix"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsd","application/rsd+xml"],["rt",["text/richtext","text/vnd.rn-realtext"]],["rtf",["application/rtf","text/richtext","application/x-rtf"]],["rtx",["text/richtext","application/rtf"]],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["saveme","application/octet-stream"],["sbk","application/x-tbook"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm",["application/vnd.lotus-screencam","video/x-scm","text/x-script.guile","application/x-lotusscreencam","text/x-script.scheme"]],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["sct","text/scriptlet"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkm","application/vnd.solent.sdkm+xml"],["sdml","text/plain"],["sdp",["application/sdp","application/x-sdp"]],["sdr","application/sounder"],["sdw","application/vnd.stardivision.writer"],["sea",["application/sea","application/x-sea"]],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["ser","application/java-serialized-object"],["set","application/set"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sgl","application/vnd.stardivision.writer-global"],["sgm",["text/sgml","text/x-sgml"]],["sgml",["text/sgml","text/x-sgml"]],["sh",["application/x-shar","application/x-bsh","application/x-sh","text/x-script.sh"]],["shar",["application/x-bsh","application/x-shar"]],["shf","application/shf+xml"],["shtml",["text/html","text/x-server-parsed-html"]],["sid","audio/x-psid"],["sis","application/vnd.symbian.install"],["sit",["application/x-stuffit","application/x-sit"]],["sitx","application/x-stuffitx"],["skd","application/x-koan"],["skm","application/x-koan"],["skp",["application/vnd.koan","application/x-koan"]],["skt","application/x-koan"],["sl","application/x-seelogo"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi",["application/smil","application/smil+xml"]],["smil","application/smil"],["snd",["audio/basic","audio/x-adpcm"]],["snf","application/x-font-snf"],["sol","application/solids"],["spc",["text/x-speech","application/x-pkcs7-certificates"]],["spf","application/vnd.yamaha.smaf-phrase"],["spl",["application/futuresplash","application/x-futuresplash"]],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spr","application/x-sprite"],["sprite","application/x-sprite"],["src","application/x-wais-source"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssi","text/x-server-parsed-html"],["ssm","application/streamingmedia"],["ssml","application/ssml+xml"],["sst",["application/vnd.ms-pkicertstore","application/vnd.ms-pki.certstore"]],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["step","application/step"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl",["application/vnd.ms-pkistl","application/sla","application/vnd.ms-pki.stl","application/x-navistyle"]],["stm","text/html"],["stp","application/step"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["sub","image/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svf",["image/vnd.dwg","image/x-dwg"]],["svg","image/svg+xml"],["svr",["x-world/x-svr","application/x-world"]],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t",["text/troff","application/x-troff"]],["talk","text/x-speech"],["tao","application/vnd.tao.intent-module-archive"],["tar","application/x-tar"],["tbk",["application/toolbook","application/x-tbook"]],["tcap","application/vnd.3gpp2.tcap"],["tcl",["text/x-script.tcl","application/x-tcl"]],["tcsh","text/x-script.tcsh"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text",["application/plain","text/plain"]],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tgz",["application/gnutar","application/x-compressed"]],["thmx","application/vnd.ms-officetheme"],["tif",["image/tiff","image/x-tiff"]],["tiff",["image/tiff","image/x-tiff"]],["tmo","application/vnd.tmobile-livetv"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","application/x-troff"],["tra","application/vnd.trueapp"],["trm","application/x-msterminal"],["tsd","application/timestamped-data"],["tsi","audio/tsp-audio"],["tsp",["application/dsptype","audio/tsplayer"]],["tsv","text/tab-separated-values"],["ttf","application/x-font-ttf"],["ttl","text/turtle"],["turbot","image/florian"],["twd","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["ufd","application/vnd.ufdl"],["uil","text/x-uil"],["uls","text/iuls"],["umj","application/vnd.umajin"],["uni","text/uri-list"],["unis","text/uri-list"],["unityweb","application/vnd.unity"],["unv","application/i-deas"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["ustar",["application/x-ustar","multipart/x-ustar"]],["utz","application/vnd.uiq.theme"],["uu",["application/octet-stream","text/x-uuencode"]],["uue","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vda","application/vda"],["vdo","video/vdo"],["vew","application/groupwise"],["vis","application/vnd.visionary"],["viv",["video/vivo","video/vnd.vivo"]],["vivo",["video/vivo","video/vnd.vivo"]],["vmd","application/vocaltec-media-desc"],["vmf","application/vocaltec-media-file"],["voc",["audio/voc","audio/x-voc"]],["vos","video/vosaic"],["vox","audio/voxware"],["vqe","audio/x-twinvq-plugin"],["vqf","audio/x-twinvq"],["vql","audio/x-twinvq-plugin"],["vrml",["model/vrml","x-world/x-vrml","application/x-vrml"]],["vrt","x-world/x-vrt"],["vsd",["application/vnd.visio","application/x-visio"]],["vsf","application/vnd.vsf"],["vst","application/x-visio"],["vsw","application/x-visio"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w60","application/wordperfect6.0"],["w61","application/wordperfect6.1"],["w6w","application/msword"],["wad","application/x-doom"],["wav",["audio/wav","audio/x-wav"]],["wax","audio/x-ms-wax"],["wb1","application/x-qpro"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/vnd.wap.wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["web","application/vnd.xara"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wiz","application/msword"],["wk1","application/x-123"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf",["windows/metafile","application/x-msmetafile"]],["wml","text/vnd.wap.wml"],["wmlc","application/vnd.wap.wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-ms-wmz"],["woff","application/x-font-woff"],["word","application/msword"],["wp","application/wordperfect"],["wp5",["application/wordperfect","application/wordperfect6.0"]],["wp6","application/wordperfect"],["wpd",["application/wordperfect","application/vnd.wordperfect","application/x-wpwin"]],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wq1","application/x-lotus"],["wqd","application/vnd.wqd"],["wri",["application/mswrite","application/x-wri","application/x-mswrite"]],["wrl",["model/vrml","x-world/x-vrml","application/x-world"]],["wrz",["model/vrml","x-world/x-vrml"]],["wsc","text/scriplet"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wsrc","application/x-wais-source"],["wtb","application/vnd.webturbo"],["wtk","application/x-wintalk"],["wvx","video/x-ms-wvx"],["x-png","image/png"],["x3d","application/vnd.hzn-3d-crossword"],["xaf","x-world/x-vrml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm",["image/xbm","image/x-xbm","image/x-xbitmap"]],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdr","video/x-amt-demorun"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xgz","xgl/drawing"],["xhtml","application/xhtml+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlam","application/vnd.ms-excel.addin.macroenabled.12"],["xlb",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlc",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xld",["application/excel","application/x-excel"]],["xlk",["application/excel","application/x-excel"]],["xll",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlm",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xls",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlsb","application/vnd.ms-excel.sheet.binary.macroenabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroenabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xltm","application/vnd.ms-excel.template.macroenabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlv",["application/excel","application/x-excel"]],["xlw",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xm","audio/xm"],["xml",["application/xml","text/xml","application/atom+xml","application/rss+xml"]],["xmz","xgl/movie"],["xo","application/vnd.olpc-sugar"],["xof","x-world/x-vrml"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpix","application/x-vnd.ls-xpix"],["xpm",["image/xpm","image/x-xpixmap"]],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xsr","video/x-amt-showrun"],["xul","application/vnd.mozilla.xul+xml"],["xwd",["image/x-xwd","image/x-xwindowdump"]],["xyz",["chemical/x-xyz","chemical/x-pdb"]],["yang","application/yang"],["yin","application/yin+xml"],["z",["application/x-compressed","application/x-compress"]],["zaz","application/vnd.zzazz.deck+xml"],["zip",["application/zip","multipart/x-zip","application/x-zip-compressed","application/x-compressed"]],["zir","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zoo","application/octet-stream"],["zsh","text/x-script.zsh"]]);e.exports={detectMimeType(e){if(!e)return a;let t=n.parse(e),i=(t.ext.substr(1)||t.name||"").split("?").shift().trim().toLowerCase(),r=a;return(o.has(i)&&(r=o.get(i)),Array.isArray(r))?r[0]:r},detectExtension(e){if(!e)return"bin";let t=(e||"").toLowerCase().trim().split("/"),i=t.shift().trim(),n=t.join("/").trim();if(r.has(i+"/"+n)){let e=r.get(i+"/"+n);return Array.isArray(e)?e[0]:e}return"text"===i?"txt":"bin"}}},79535:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return r},normalizeRscURL:function(){return o}});let n=i(90372),a=i(84212);function r(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,i,n)=>!t||(0,a.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84212:(e,t)=>{"use strict";function i(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function a(e,t){if(e.includes(r)){let e=JSON.stringify(t);return"{}"!==e?r+"?"+e:r}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return r},addSearchParamsIfPageSegment:function(){return a},isGroupSegment:function(){return i},isParallelRouteSegment:function(){return n}});let r="__PAGE__",o="__DEFAULT__"},85909:(e,t,i)=>{"use strict";let n=i(79646).spawn,a=i(91423),r=i(89581);class o{constructor(e){e=e||{},this._spawn=n,this.options=e||{},this.name="Sendmail",this.version=a.version,this.path="sendmail",this.args=!1,this.winbreak=!1,this.logger=r.getLogger(this.options,{component:this.options.component||"sendmail"}),e&&("string"==typeof e?this.path=e:"object"==typeof e&&(e.path&&(this.path=e.path),Array.isArray(e.args)&&(this.args=e.args),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())))}send(e,t){let i,n,a;e.message.keepBcc=!0;let r=e.data.envelope||e.message.getEnvelope(),o=e.message.messageId();if([].concat(r.from||[]).concat(r.to||[]).some(e=>/^-/.test(e)))return t(Error("Can not send mail. Invalid envelope addresses."));i=this.args?["-i"].concat(this.args).concat(r.to):["-i"].concat(r.from?["-f",r.from]:[]).concat(r.to);let s=i=>{if(!a&&(a=!0,"function"==typeof t))if(i)return t(i);else return t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:o,response:"Messages queued for delivery"})};try{n=this._spawn(this.path,i)}catch(e){return this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred while spawning sendmail. %s",e.message),s(e)}if(!n)return s(Error("sendmail was not found"));{n.on("error",e=>{this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred when sending message %s. %s",o,e.message),s(e)}),n.once("exit",e=>{let t;if(!e)return s();t=127===e?Error("Sendmail command not found, process exited with code "+e):Error("Sendmail exited with code "+e),this.logger.error({err:t,tnx:"stdin",messageId:o},"Error sending message %s to sendmail. %s",o,t.message),s(t)}),n.once("close",s),n.stdin.on("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when piping message %s to sendmail. %s",o,e.message),s(e)});let t=[].concat(r.to||[]);t.length>3&&t.push("...and "+t.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:o},"Sending message %s to <%s>",o,t.join(", "));let i=e.message.createReadStream();i.once("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when generating message %s. %s",o,e.message),n.kill("SIGINT"),s(e)}),i.pipe(n.stdin)}}}e.exports=o},88477:(e,t)=>{"use strict";function i(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let i=0;i<=e.length-t.length;i++){let n=!0;for(let a=0;a<t.length;a++)if(e[i+a]!==t[a]){n=!1;break}if(n)return i}return -1}function n(e,t){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}function a(e,t){let n=i(e,t);if(0===n)return e.subarray(t.length);if(!(n>-1))return e;{let i=new Uint8Array(e.length-t.length);return i.set(e.slice(0,n)),i.set(e.slice(n+t.length),n),i}}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{indexOfUint8Array:function(){return i},isEquivalentUint8Arrays:function(){return n},removeFromUint8Array:function(){return a}})},89581:(e,t,i)=>{"use strict";let n,a=i(79551),r=i(28354),o=i(29021),s=i(29564),l=i(37366),c=i(91645),p=i(21820);try{n=p.networkInterfaces()}catch(e){}e.exports.networkInterfaces=n;let d=(t,i)=>{let n=e.exports.networkInterfaces;return!n||Object.keys(n).map(e=>n[e]).reduce((e,t)=>e.concat(t),[]).filter(e=>!e.internal||i).filter(e=>e.family==="IPv"+t||e.family===t).length>0},u=(e,t,i,n)=>{if(!d(e,(i=i||{}).allowInternalNetworkInterfaces))return n(null,[]);(l.Resolver?new l.Resolver(i):l)["resolve"+e](t,(e,t)=>{if(e){switch(e.code){case l.NODATA:case l.NOTFOUND:case l.NOTIMP:case l.SERVFAIL:case l.CONNREFUSED:case l.REFUSED:case"EAI_AGAIN":return n(null,[])}return n(e)}return n(null,Array.isArray(t)?t:[].concat(t||[]))})},m=e.exports.dnsCache=new Map,h=(e,t)=>e?Object.assign({servername:e.servername,host:e.addresses&&e.addresses.length?1===e.addresses.length?e.addresses[0]:e.addresses[Math.floor(Math.random()*e.addresses.length)]:null},t||{}):Object.assign({},t||{});function f(e,t){let i=!1,n=[],a=0;e.on("error",e=>{i||(i=!0,t(e))}),e.on("readable",()=>{let t;for(;null!==(t=e.read());)n.push(t),a+=t.length}),e.on("end",()=>{let e;if(!i){i=!0;try{e=Buffer.concat(n,a)}catch(e){return t(e)}t(null,e)}})}e.exports.resolveHostname=(e,t)=>{let i;return(!(e=e||{}).host&&e.servername&&(e.host=e.servername),!e.host||c.isIP(e.host))?t(null,h({addresses:[e.host],servername:e.servername||!1},{cached:!1})):m.has(e.host)&&(!(i=m.get(e.host)).expires||i.expires>=Date.now())?t(null,h(i.value,{cached:!0})):void u(4,e.host,e,(n,a)=>{if(n)return i?t(null,h(i.value,{cached:!0,error:n})):t(n);if(a&&a.length){let i={addresses:a,servername:e.servername||e.host};return m.set(e.host,{value:i,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,h(i,{cached:!1}))}u(6,e.host,e,(n,a)=>{if(n)return i?t(null,h(i.value,{cached:!0,error:n})):t(n);if(a&&a.length){let i={addresses:a,servername:e.servername||e.host};return m.set(e.host,{value:i,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,h(i,{cached:!1}))}try{l.lookup(e.host,{all:!0},(n,a)=>{if(n)return i?t(null,h(i.value,{cached:!0,error:n})):t(n);let r=!!a&&a.filter(e=>d(e.family)).map(e=>e.address).shift();if(a&&a.length&&!r&&console.warn(`Failed to resolve IPv${a[0].family} addresses with current network`),!r&&i)return t(null,h(i.value,{cached:!0}));let o={addresses:r?[r]:[e.host],servername:e.servername||e.host};return m.set(e.host,{value:o,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,h(o,{cached:!1}))})}catch(e){if(i)return t(null,h(i.value,{cached:!0,error:e}));return t(e)}})})},e.exports.parseConnectionUrl=e=>{e=e||"";let t={};return[a.parse(e,!0)].forEach(e=>{let i;switch(e.protocol){case"smtp:":t.secure=!1;break;case"smtps:":t.secure=!0;break;case"direct:":t.direct=!0}!isNaN(e.port)&&Number(e.port)&&(t.port=Number(e.port)),e.hostname&&(t.host=e.hostname),e.auth&&(i=e.auth.split(":"),t.auth||(t.auth={}),t.auth.user=i.shift(),t.auth.pass=i.join(":")),Object.keys(e.query||{}).forEach(i=>{let n=t,a=i,r=e.query[i];switch(!isNaN(r)&&(r=Number(r)),r){case"true":r=!0;break;case"false":r=!1}if(0===i.indexOf("tls."))a=i.substr(4),t.tls||(t.tls={}),n=t.tls;else if(i.indexOf(".")>=0)return;a in n||(n[a]=r)})}),t},e.exports._logFunc=(e,t,i,n,a,...r)=>{let o={};Object.keys(i||{}).forEach(e=>{"level"!==e&&(o[e]=i[e])}),Object.keys(n||{}).forEach(e=>{"level"!==e&&(o[e]=n[e])}),e[t](o,a,...r)},e.exports.getLogger=(t,i)=>{let n={},a=["trace","debug","info","warn","error","fatal"];if(!(t=t||{}).logger)return a.forEach(e=>{n[e]=()=>!1}),n;let o=t.logger;return!0===t.logger&&(o=function(e){let t=0,i=new Map;e.forEach(e=>{e.length>t&&(t=e.length)}),e.forEach(e=>{let n=e.toUpperCase();n.length<t&&(n+=" ".repeat(t-n.length)),i.set(e,n)});let n=(e,t,n,...a)=>{let o="";t&&("server"===t.tnx?o="S: ":"client"===t.tnx&&(o="C: "),t.sid&&(o="["+t.sid+"] "+o),t.cid&&(o="[#"+t.cid+"] "+o)),(n=r.format(n,...a)).split(/\r?\n/).forEach(t=>{console.log("[%s] %s %s",new Date().toISOString().substr(0,19).replace(/T/," "),i.get(e),o+t)})},a={};return e.forEach(e=>{a[e]=n.bind(null,e)}),a}(a)),a.forEach(t=>{n[t]=(n,a,...r)=>{e.exports._logFunc(o,t,i,n,a,...r)}}),n},e.exports.callbackPromise=(e,t)=>function(){let i=Array.from(arguments),n=i.shift();n?t(n):e(...i)},e.exports.parseDataURI=e=>{let t,i=e.indexOf(",");if(!i)return e;let n=e.substring(i+1),a=e.substring(5,i).split(";"),r=a.length>1&&a[a.length-1];r&&0>r.indexOf("=")&&(t=r.toLowerCase(),a.pop());let o=a.shift()||"application/octet-stream",s={};for(let e of a){let t=e.indexOf("=");if(t>=0){let i=e.substring(0,t),n=e.substring(t+1);s[i]=n}}switch(t){case"base64":n=Buffer.from(n,"base64");break;case"utf8":n=Buffer.from(n);break;default:try{n=Buffer.from(decodeURIComponent(n))}catch(e){n=Buffer.from(n)}n=Buffer.from(n)}return{data:n,encoding:t,contentType:o,params:s}},e.exports.resolveContent=(t,i,n)=>{let a;n||(a=new Promise((t,i)=>{n=e.exports.callbackPromise(t,i)}));let r=t&&t[i]&&t[i].content||t[i],l=("object"==typeof t[i]&&t[i].encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");if(!r)return n(null,r);if("object"==typeof r){if("function"==typeof r.pipe)return f(r,(e,a)=>{if(e)return n(e);t[i].content?t[i].content=a:t[i]=a,n(null,a)});else if(/^https?:\/\//i.test(r.path||r.href))return f(s(r.path||r.href),n);else if(/^data:/i.test(r.path||r.href)){let t=e.exports.parseDataURI(r.path||r.href);return t&&t.data?n(null,t.data):n(null,Buffer.from(0))}else if(r.path)return f(o.createReadStream(r.path),n)}return"string"!=typeof t[i].content||["utf8","usascii","ascii"].includes(l)||(r=Buffer.from(t[i].content,l)),setImmediate(()=>n(null,r)),a},e.exports.assign=function(){let e=Array.from(arguments),t=e.shift()||{};return e.forEach(e=>{Object.keys(e||{}).forEach(i=>{["tls","auth"].includes(i)&&e[i]&&"object"==typeof e[i]?(t[i]||(t[i]={}),Object.keys(e[i]).forEach(n=>{t[i][n]=e[i][n]})):t[i]=e[i]})}),t},e.exports.encodeXText=e=>{if(!/[^\x21-\x2A\x2C-\x3C\x3E-\x7E]/.test(e))return e;let t=Buffer.from(e),i="";for(let e=0,n=t.length;e<n;e++){let n=t[e];n<33||n>126||43===n||61===n?i+="+"+(n<16?"0":"")+n.toString(16).toUpperCase():i+=String.fromCharCode(n)}return i}},90372:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},90762:(e,t,i)=>{"use strict";i.r(t),i.d(t,{"6092811ebc6b79ca629690c3d3930d04724eef3df3":()=>r});var n=i(91199);i(42087);var a=i(41635);async function r(e,t){let i=t.get("name"),n=t.get("email"),r=t.get("subject"),o=t.get("message"),s={};if(i||(s.name=["Name is required."]),n?/\S+@\S+\.\S+/.test(n)||(s.email=["Invalid email address."]):s.email=["Email is required."],r||(s.subject=["Subject is required."]),o||(s.message=["Message is required."]),Object.keys(s).length>0)return{message:"Form submission failed. Please check the errors below.",success:!1,errors:s};let{SMTP_HOST:l,SMTP_PORT:c,SMTP_USER:p,SMTP_PASS:d,CONTACT_FORM_RECIPIENT:u}=process.env;if(!l||!c||!p||!d||!u)return console.error("SMTP environment variables are not fully configured."),{message:"Server configuration error. Could not send message. Please try again later.",success:!1};let m=a.createTransport({host:l,port:parseInt(c,10),secure:465===parseInt(c,10),auth:{user:p,pass:d}});try{return await m.sendMail({from:`"${i}" <${p}>`,replyTo:n,to:u,subject:`New Contact Form: ${r}`,text:`You have received a new message from your website contact form:


Name: ${i}

Email: ${n}

Subject: ${r}

Message:
${o}`,html:`<p>You have received a new message from your website contact form:</p>
             <ul>
               <li><strong>Name:</strong> ${i}</li>
               <li><strong>Email:</strong> ${n}</li>
               <li><strong>Subject:</strong> ${r}</li>
             </ul>
             <p><strong>Message:</strong></p>
             <p>${o.replace(/\n/g,"<br>")}</p>`}),{message:"Message sent successfully! We will get back to you soon.",success:!0}}catch(e){return console.error("Failed to send email:",e),{message:"Failed to send message. Please try again later.",success:!1}}}(0,i(33331).D)([r]),(0,n.A)(r,"6092811ebc6b79ca629690c3d3930d04724eef3df3",null)},91199:(e,t,i)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=i(74932)},91423:e=>{"use strict";e.exports=JSON.parse('{"name":"nodemailer","version":"7.0.3","description":"Easy as cake e-mail sending from your Node.js applications","main":"lib/nodemailer.js","scripts":{"test":"node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","test:coverage":"c8 node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","lint":"eslint .","update":"rm -rf node_modules/ package-lock.json && ncu -u && npm install"},"repository":{"type":"git","url":"https://github.com/nodemailer/nodemailer.git"},"keywords":["Nodemailer"],"author":"Andris Reinman","license":"MIT-0","bugs":{"url":"https://github.com/nodemailer/nodemailer/issues"},"homepage":"https://nodemailer.com/","devDependencies":{"@aws-sdk/client-sesv2":"3.804.0","bunyan":"1.8.15","c8":"10.1.3","eslint":"8.57.0","eslint-config-nodemailer":"1.2.0","eslint-config-prettier":"9.1.0","libbase64":"1.3.0","libmime":"5.3.6","libqp":"2.1.1","nodemailer-ntlm-auth":"1.0.4","proxy":"1.0.2","proxy-test-server":"1.0.0","smtp-server":"3.13.6"},"engines":{"node":">=6.0.0"}}')},91551:(e,t,i)=>{"use strict";let n=i(12894),a=i(97979),r=i(26434),o=i(27910).PassThrough,s=i(29021),l=i(33873),c=i(55511);class p{constructor(e,t,i,n){this.options=e||{},this.keys=t,this.cacheTreshold=Number(this.options.cacheTreshold)||2097152,this.hashAlgo=this.options.hashAlgo||"sha256",this.cacheDir=this.options.cacheDir||!1,this.chunks=[],this.chunklen=0,this.readPos=0,this.cachePath=!!this.cacheDir&&l.join(this.cacheDir,"message."+Date.now()+"-"+c.randomBytes(14).toString("hex")),this.cache=!1,this.headers=!1,this.bodyHash=!1,this.parser=!1,this.relaxedBody=!1,this.input=i,this.output=n,this.output.usingCache=!1,this.hasErrored=!1,this.input.on("error",e=>{this.hasErrored=!0,this.cleanup(),n.emit("error",e)})}cleanup(){this.cache&&this.cachePath&&s.unlink(this.cachePath,()=>!1)}createReadCache(){this.cache=s.createReadStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.output.emit("error",e)}),this.cache.once("close",()=>{this.cleanup()}),this.cache.pipe(this.output)}sendNextChunk(){if(this.hasErrored)return;if(this.readPos>=this.chunks.length)return this.cache?this.createReadCache():this.output.end();let e=this.chunks[this.readPos++];if(!1===this.output.write(e))return this.output.once("drain",()=>{this.sendNextChunk()});setImmediate(()=>this.sendNextChunk())}sendSignedOutput(){let e=0,t=()=>{if(e>=this.keys.length)return this.output.write(this.parser.rawHeaders),setImmediate(()=>this.sendNextChunk());let i=this.keys[e++],n=r(this.headers,this.hashAlgo,this.bodyHash,{domainName:i.domainName,keySelector:i.keySelector,privateKey:i.privateKey,headerFieldNames:this.options.headerFieldNames,skipFields:this.options.skipFields});return n&&this.output.write(Buffer.from(n+"\r\n")),setImmediate(t)};if(this.bodyHash&&this.headers)return t();this.output.write(this.parser.rawHeaders),this.sendNextChunk()}createWriteCache(){this.output.usingCache=!0,this.cache=s.createWriteStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.relaxedBody.unpipe(this.cache),this.relaxedBody.on("readable",()=>{for(;null!==this.relaxedBody.read(););}),this.hasErrored=!0,this.output.emit("error",e)}),this.cache.once("close",()=>{this.sendSignedOutput()}),this.relaxedBody.removeAllListeners("readable"),this.relaxedBody.pipe(this.cache)}signStream(){this.parser=new n,this.relaxedBody=new a({hashAlgo:this.hashAlgo}),this.parser.on("headers",e=>{this.headers=e}),this.relaxedBody.on("hash",e=>{this.bodyHash=e}),this.relaxedBody.on("readable",()=>{let e;if(!this.cache){for(;null!==(e=this.relaxedBody.read());)if(this.chunks.push(e),this.chunklen+=e.length,this.chunklen>=this.cacheTreshold&&this.cachePath)return this.createWriteCache()}}),this.relaxedBody.on("end",()=>{this.cache||this.sendSignedOutput()}),this.parser.pipe(this.relaxedBody),setImmediate(()=>this.input.pipe(this.parser))}}class d{constructor(e){this.options=e||{},this.keys=[].concat(this.options.keys||{domainName:e.domainName,keySelector:e.keySelector,privateKey:e.privateKey})}sign(e,t){let i=new o,n=e,a=!1;Buffer.isBuffer(e)?(a=e,n=new o):"string"==typeof e&&(a=Buffer.from(e),n=new o);let r=this.options;t&&Object.keys(t).length&&(r={},Object.keys(this.options||{}).forEach(e=>{r[e]=this.options[e]}),Object.keys(t||{}).forEach(e=>{e in r||(r[e]=t[e])}));let s=new p(r,this.keys,n,i);return setImmediate(()=>{s.signStream(),a&&setImmediate(()=>{n.end(a)})}),i}}e.exports=d},91645:e=>{"use strict";e.exports=require("net")},91677:e=>{"use strict";class t{constructor(e){this.str=(e||"").toString(),this.operatorCurrent="",this.operatorExpecting="",this.node=null,this.escaped=!1,this.list=[],this.operators={'"':'"',"(":")","<":">",",":"",":":";",";":""}}tokenize(){let e=[];for(let e=0,t=this.str.length;e<t;e++){let i=this.str.charAt(e),n=e<t-1?this.str.charAt(e+1):null;this.checkChar(i,n)}return this.list.forEach(t=>{t.value=(t.value||"").toString().trim(),t.value&&e.push(t)}),e}checkChar(e,t){if(this.escaped);else if(e===this.operatorExpecting){this.node={type:"operator",value:e},t&&![" ","	","\r","\n",",",";"].includes(t)&&(this.node.noBreak=!0),this.list.push(this.node),this.node=null,this.operatorExpecting="",this.escaped=!1;return}else if(!this.operatorExpecting&&e in this.operators){this.node={type:"operator",value:e},this.list.push(this.node),this.node=null,this.operatorExpecting=this.operators[e],this.escaped=!1;return}else if(['"',"'"].includes(this.operatorExpecting)&&"\\"===e){this.escaped=!0;return}this.node||(this.node={type:"text",value:""},this.list.push(this.node)),"\n"===e&&(e=" "),(e.charCodeAt(0)>=33||[" ","	"].includes(e))&&(this.node.value+=e),this.escaped=!1}}e.exports=function e(i,n){n=n||{};let a=new t(i).tokenize(),r=[],o=[],s=[];if(a.forEach(e=>{"operator"===e.type&&(","===e.value||";"===e.value)?(o.length&&r.push(o),o=[]):o.push(e)}),o.length&&r.push(o),r.forEach(t=>{(t=function(t){let i,n,a,r=!1,o="text",s=[],l={address:[],comment:[],group:[],text:[]};for(n=0,a=t.length;n<a;n++){let e=t[n],i=n?t[n-1]:null;if("operator"===e.type)switch(e.value){case"<":o="address";break;case"(":o="comment";break;case":":o="group",r=!0;break;default:o="text"}else e.value&&("address"===o&&(e.value=e.value.replace(/^[^<]*<\s*/,"")),i&&i.noBreak&&l[o].length?l[o][l[o].length-1]+=e.value:l[o].push(e.value))}if(!l.text.length&&l.comment.length&&(l.text=l.comment,l.comment=[]),r)l.text=l.text.join(" "),s.push({name:l.text||i&&i.name,group:l.group.length?e(l.group.join(",")):[]});else{if(!l.address.length&&l.text.length){for(n=l.text.length-1;n>=0;n--)if(l.text[n].match(/^[^@\s]+@[^@\s]+$/)){l.address=l.text.splice(n,1);break}let e=function(e){return l.address.length?e:(l.address=[e.trim()]," ")};if(!l.address.length)for(n=l.text.length-1;n>=0&&(l.text[n]=l.text[n].replace(/\s*\b[^@\s]+@[^\s]+\b\s*/,e).trim(),!l.address.length);n--);}if(!l.text.length&&l.comment.length&&(l.text=l.comment,l.comment=[]),l.address.length>1&&(l.text=l.text.concat(l.address.splice(1))),l.text=l.text.join(" "),l.address=l.address.join(" "),!l.address&&r)return[];(i={address:l.address||l.text||"",name:l.text||l.address||""}).address===i.name&&((i.address||"").match(/@/)?i.name="":i.address=""),s.push(i)}return s}(t)).length&&(s=s.concat(t))}),n.flatten){let e=[],t=i=>{i.forEach(i=>{if(i.group)return t(i.group);e.push(i)})};return t(s),e}return s}},93128:(e,t,i)=>{Promise.resolve().then(i.bind(i,5872))},93384:(e,t,i)=>{"use strict";let n=i(12501),a=i(89581).assign,r=i(1573),o=i(94735);class s extends o{constructor(e){if(super(),this.pool=e,this.options=e.options,this.logger=this.pool.logger,this.options.auth)switch((this.options.auth.type||"").toString().toUpperCase()){case"OAUTH2":{let e=new r(this.options.auth,this.logger);e.provisionCallback=this.pool.mailer&&this.pool.mailer.get("oauth2_provision_cb")||e.provisionCallback,this.auth={type:"OAUTH2",user:this.options.auth.user,oauth2:e,method:"XOAUTH2"},e.on("token",e=>this.pool.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e));break}default:if(!this.options.auth.user&&!this.options.auth.pass)break;this.auth={type:(this.options.auth.type||"").toString().toUpperCase()||"LOGIN",user:this.options.auth.user,credentials:{user:this.options.auth.user||"",pass:this.options.auth.pass,options:this.options.auth.options},method:(this.options.auth.method||"").trim().toUpperCase()||this.options.authMethod||!1}}this._connection=!1,this._connected=!1,this.messages=0,this.available=!0}connect(e){this.pool.getSocket(this.options,(t,i)=>{if(t)return e(t);let r=!1,o=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:o.host||"",destPort:o.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,o.host||"",o.port||""),o=a(!1,o),Object.keys(i).forEach(e=>{o[e]=i[e]})),this.connection=new n(o),this.connection.once("error",t=>{if(this.emit("error",t),!r)return r=!0,e(t)}),this.connection.once("end",()=>{if(this.close(),r)return;r=!0;let t=setTimeout(()=>{if(r)return;let t=Error("Unexpected socket close");this.connection&&this.connection._socket&&this.connection._socket.upgrading&&(t.code="ETLS"),e(t)},1e3);try{t.unref()}catch(e){}}),this.connection.connect(()=>{if(!r)if(!this.auth||!this.connection.allowsAuth&&!o.forceAuth)return r=!0,this._connected=!0,e(null,!0);else this.connection.login(this.auth,t=>{if(!r){if(r=!0,t)return this.connection.close(),this.emit("error",t),e(t);this._connected=!0,e(null,!0)}})})})}send(e,t){if(!this._connected)return this.connect(i=>i?t(i):this.send(e,t));let i=e.message.getEnvelope(),n=e.message.messageId(),a=[].concat(i.to||[]);a.length>3&&a.push("...and "+a.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:n,cid:this.id},"Sending message %s using #%s to <%s>",n,this.id,a.join(", ")),e.data.dsn&&(i.dsn=e.data.dsn),this.connection.send(i,e.message.createReadStream(),(e,a)=>{if(this.messages++,e)return this.connection.close(),this.emit("error",e),t(e);a.envelope={from:i.from,to:i.to},a.messageId=n,setImmediate(()=>{let e;this.messages>=this.options.maxMessages?((e=Error("Resource exhausted")).code="EMAXLIMIT",this.connection.close(),this.emit("error",e)):this.pool._checkRateLimit(()=>{this.available=!0,this.emit("available")})}),t(null,a)})}close(){this._connected=!1,this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.connection&&this.connection.close(),this.emit("close")}}e.exports=s},94735:e=>{"use strict";e.exports=require("events")},96002:(e,t,i)=>{"use strict";let n=i(91423),a=i(89581);class r{constructor(e){e=e||{},this.options=e||{},this.name="StreamTransport",this.version=n.version,this.logger=a.getLogger(this.options,{component:this.options.component||"stream-transport"}),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),n=e.message.messageId(),a=[].concat(i.to||[]);a.length>3&&a.push("...and "+a.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:n},"Sending message %s to <%s> using %s line breaks",n,a.join(", "),this.winbreak?"<CR><LF>":"<LF>"),setImmediate(()=>{let i;try{i=e.message.createReadStream()}catch(e){return this.logger.error({err:e,tnx:"send",messageId:n},"Creating send stream failed for %s. %s",n,e.message),t(e)}if(!this.options.buffer)return i.once("error",e=>{this.logger.error({err:e,tnx:"send",messageId:n},"Failed creating message for %s. %s",n,e.message)}),t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:n,message:i});let a=[],r=0;i.on("readable",()=>{let e;for(;null!==(e=i.read());)a.push(e),r+=e.length}),i.once("error",e=>(this.logger.error({err:e,tnx:"send",messageId:n},"Failed creating message for %s. %s",n,e.message),t(e))),i.on("end",()=>t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:n,message:Buffer.concat(a,r)}))})}}e.exports=r},97748:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{atLeastOneTask:function(){return a},scheduleImmediate:function(){return n},scheduleOnNextTick:function(){return i},waitAtLeastOneReactRenderTask:function(){return r}});let i=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},n=e=>{setImmediate(e)};function a(){return new Promise(e=>n(e))}function r(){return new Promise(e=>setImmediate(e))}},97979:(e,t,i)=>{"use strict";let n=i(27910).Transform,a=i(55511);class r extends n{constructor(e){super(),e=e||{},this.chunkBuffer=[],this.chunkBufferLen=0,this.bodyHash=a.createHash(e.hashAlgo||"sha1"),this.remainder="",this.byteLength=0,this.debug=e.debug,this._debugBody=!!e.debug&&[]}updateHash(e){let t,i="",n="file";for(let t=e.length-1;t>=0;t--){let a=e[t];if("file"===n&&(10===a||13===a));else if("file"===n&&(9===a||32===a))n="line";else if("line"===n&&(9===a||32===a));else if(("file"===n||"line"===n)&&(n="body",t===e.length-1))break;if(0===t){if("file"===n&&(!this.remainder||/[\r\n]$/.test(this.remainder))||"line"===n&&(!this.remainder||/[ \t]$/.test(this.remainder))){this.remainder+=e.toString("binary");return}else if("line"===n||"file"===n){i=e.toString("binary"),e=!1;break}}if("body"===n){i=e.slice(t+1).toString("binary"),e=e.slice(0,t+1);break}}let a=!!this.remainder;if(e&&!a){for(let t=0,i=e.length;t<i;t++)if(t&&10===e[t]&&13!==e[t-1]){a=!0;break}else if(t&&13===e[t]&&32===e[t-1]){a=!0;break}else if(t&&32===e[t]&&32===e[t-1]){a=!0;break}else if(9===e[t]){a=!0;break}}a?(t=this.remainder+(e?e.toString("binary"):""),this.remainder=i,t=t.replace(/\r?\n/g,"\n").replace(/[ \t]*$/gm,"").replace(/[ \t]+/gm," ").replace(/\n/g,"\r\n"),e=Buffer.from(t,"binary")):i&&(this.remainder=i),this.debug&&this._debugBody.push(e),this.bodyHash.update(e)}_transform(e,t,i){if(!e||!e.length)return i();"string"==typeof e&&(e=Buffer.from(e,t)),this.updateHash(e),this.byteLength+=e.length,this.push(e),i()}_flush(e){/[\r\n]$/.test(this.remainder)&&this.byteLength>2&&this.bodyHash.update(Buffer.from("\r\n")),this.byteLength||this.push(Buffer.from("\r\n")),this.emit("hash",this.bodyHash.digest("base64"),!!this.debug&&Buffer.concat(this._debugBody)),e()}}e.exports=r}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,6391,2975,8446,270],()=>i(22226));module.exports=n})();